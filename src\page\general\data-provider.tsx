import { useGet } from '@/hooks/useGet';
import { defaultSetting } from '@/lib/constants';
import { differenceInDays } from 'date-fns';
import { createContext, useContext, useEffect, useState } from 'react';

type DataProviderProps = {
  children: React.ReactNode;
};

type DataProviderState = {
  staffTypes: IStaffType[];
  tasks: ITask[];
  taskTypes: ITaskType[];
  date: Date;
  setDate: (arg: Date) => void;
  isEditable: boolean;
  staffs: IStaff[];
  setStaffs: (arg: IStaff[]) => void;
  orders: number[];
  setOrders: (arg: number[]) => void;
  settingData: ISettingData;
};

const initialState: DataProviderState = {
  staffTypes: [],
  tasks: [],
  taskTypes: [],
  date: new Date(),
  setDate: () => null,
  isEditable: false,
  staffs: [],
  setStaffs: () => null,
  orders: [],
  setOrders: () => null,
  settingData: defaultSetting,
};

const DataProviderContext = createContext<DataProviderState>(initialState);

export function DataProvider({ children }: DataProviderProps) {
  const [staffTypes, setStaffTypes] = useState<IStaffType[]>([]);
  const [tasks, setTasks] = useState<ITask[]>([]);
  const [taskTypes, setTaskTypes] = useState<ITaskType[]>([]);
  const [date, setDate] = useState(new Date());
  const [staffs, setStaffs] = useState<IStaff[]>([]);
  const [orders, setOrders] = useState<number[]>([]);
  const [settingData, setSettingData] = useState<ISettingData>(defaultSetting);

  const { data: sdData } = useGet({
    url: '/user/setting',
    params: { dateOffset: differenceInDays(new Date(), date) },
  });

  const { data: stData } = useGet({ url: '/staff/type' });
  const { data: tData } = useGet({ url: '/task' });
  const { data: ttData } = useGet({ url: '/task/type' });

  // const { isPending, data, refetch } = useStaffDate(date);

  useEffect(() => {
    if (sdData) {
      setSettingData(sdData);
    }
  }, [sdData]);

  useEffect(() => {
    if (stData?.length > 0) {
      setStaffTypes(stData);
    }
  }, [stData]);

  useEffect(() => {
    if (tData?.length > 0) {
      setTasks(tData);
    }
  }, [tData]);

  useEffect(() => {
    if (ttData?.length > 0) {
      setTaskTypes(ttData);
    }
  }, [ttData]);

  const value = {
    staffTypes,
    tasks,
    taskTypes,
    date,
    setDate,
    isEditable: false,
    staffs,
    setStaffs,
    orders,
    setOrders,
    settingData,
  };

  return <DataProviderContext.Provider value={value}>{children}</DataProviderContext.Provider>;
}

export const useData = () => {
  const context = useContext(DataProviderContext);

  if (context === undefined) throw new Error('useTheme must be used within a ThemeProvider');

  return context;
};
