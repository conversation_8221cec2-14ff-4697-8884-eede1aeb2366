import { AddBusy } from "./crud/busy-add";
import { ComebackWork } from "./crud/comeback";
import { EndWork } from "./crud/end-work";
import { LeaveWork } from "./crud/leave-work";
import { SkipWork } from "./crud/skip-work";
import { StartWork } from "./crud/start-work";
import { RemoveBusy } from "./crud/busy-remove";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { useData } from "./data-provider";
import { usePost } from "@/hooks/usePost";
import { useEffect, useState } from "react";
import { useToast } from "@/components/ui/use-toast";
import { Loader2 } from "lucide-react";

type ComponentProps = {
  staff: IStaff;
  refetch: () => void;
};

function WorkGroupBtn({ staff, refetch }: ComponentProps) {
  const [loading, setLoading] = useState(false);
  const [open, setOpen] = useState(false);

  const { tasks } = useData();

  useEffect(() => {
    if (loading) {
      setOpen(false);
    }
  }, [loading]);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button disabled={loading} size="sm" variant="default" className="m-1">
          {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          Lựa chọn
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-64">
        <div className="flex flex-wrap justify-center items-center">
          <StartWork refetch={refetch} staff={staff} />
          <SkipWork refetch={refetch} staff={staff} />
          <AddBusy refetch={refetch} staff={staff} />
          <LeaveWork refetch={refetch} staff={staff} />

          {tasks
            .filter((el) => !el.taskType?.isDelete)
            .filter((el) => el.isLove)
            .sort((a, b) => a.taskTypeId - b.taskTypeId)
            .map((task, index) => (
              <WorkStartShort
                staff={staff}
                key={index}
                task={task}
                setLoading={setLoading}
                refetch={refetch}
              />
            ))}
        </div>
      </PopoverContent>
    </Popover>
  );
}

type WorkStartProps = {
  staff: IStaff;
  task: ITask;
  refetch: () => void;
  setLoading: (arg: boolean) => void;
};

function WorkStartShort({ staff, task, refetch, setLoading }: WorkStartProps) {
  const { toast } = useToast();

  const { mutate } = usePost({
    url: "/work/start",
    onSuccess: () => {
      setLoading(false);
      refetch();
    },
    onError: () => {
      toast({
        variant: "destructive",
        description: "Có lỗi xảy ra. Vui lòng reload lại trang.",
      });
      setLoading(false);
    },
  });

  return (
    <Button
      onClick={() => {
        mutate({
          staffId: staff.id,
          tasks: [task.id],
          customerRequest: false,
          startAfterSec: 0,
        });

        setLoading(true);
      }}
      size="sm"
      variant="default"
      className="m-1"
    >
      {task.name}
    </Button>
  );
}

export function WorkCtrl({ staff, refetch }: ComponentProps) {
  const info = staff.today;

  return (
    <div>
      {info?.isBusy ? (
        <RemoveBusy refetch={refetch} staff={staff} />
      ) : info?.isLeave ? (
        <ComebackWork refetch={refetch} staff={staff} />
      ) : info?.onWorking ? (
        <EndWork refetch={refetch} staff={staff} />
      ) : (
        <WorkGroupBtn refetch={refetch} staff={staff} />
      )}
    </div>
  );
}
