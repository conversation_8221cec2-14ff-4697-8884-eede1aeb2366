import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { useEffect, useMemo, useRef, useState } from "react";

import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  She<PERSON><PERSON><PERSON>le,
  SheetTrigger,
} from "@/components/ui/sheet";
import { Label } from "@/components/ui/label";
import { differenceInDays, format } from "date-fns";
import { useGet } from "@/hooks/useGet";
import { useData } from "../data-provider";
import { usePost } from "@/hooks/usePost";

export function Note() {
  const [content, setContent] = useState("");
  const { date } = useData();
  const timeoutRef = useRef<ReturnType<typeof setTimeout>>();

  const dateOffset = useMemo(() => differenceInDays(new Date(), date), [date]);

  const { data } = useGet({ url: "/user/get-note", params: { dateOffset } });
  const { mutateAsync: saveNote } = usePost({ url: "/user/add-note" });

  useEffect(() => {
    if (data?.content) {
      setContent(data.content);
    }
  }, [data]);

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const _content = e.target.value;

    setContent(_content);
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      handleSave(_content);
    }, 500);
  };

  const handleSave = async (content: string) => {
    try {
      await saveNote({ content });
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <Sheet modal={false}>
      <SheetTrigger asChild>
        <Button variant="outline" className="relative">
          Note
        </Button>
      </SheetTrigger>
      <SheetContent
        onInteractOutside={(e) => e.preventDefault()}
        className="max-w-4xl"
      >
        <SheetHeader>
          <SheetTitle>Note</SheetTitle>
          <SheetDescription></SheetDescription>
        </SheetHeader>

        {dateOffset != 0 && (!data || data.content == "") ? (
          <div className="mt-4 h-full">
            Không có note ngày {format(date, "yyyy/MM/dd")}
          </div>
        ) : (
          <div className="mt-4 h-full">
            <Label className="text-xl" htmlFor="message">
              {format(date, "yyyy/MM/dd")}
            </Label>
            <Textarea
              value={content}
              disabled={dateOffset !== 0}
              onChange={handleChange}
              className="h-[calc(100vh-200px)] text-2xl"
              placeholder="Viết note ở đây..."
            />
          </div>
        )}
      </SheetContent>
    </Sheet>
  );
}
