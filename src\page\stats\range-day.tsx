import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useGet } from '@/hooks/useGet';
import { useStaffType } from '@/hooks/useStaffType';

import { cn } from '@/lib/utils';

import { differenceInDays, format, subYears } from 'date-fns';
import { ArrowBigRight, ArrowUpDown, Calendar as CalendarIcon } from 'lucide-react';

import { useEffect, useState } from 'react';

export function StatsRangeDay() {
  const [fromDate, setFromDate] = useState<Date | undefined>(subYears(new Date(), 1));
  const [fromCalendarOpen, setFromCalendarOpen] = useState(false);

  const [toDate, setToDate] = useState<Date | undefined>(new Date());
  const [toCalendarOpen, setToCalendarOpen] = useState(false);

  const [statsData, setStatsData] = useState<StatsData[]>([]);
  const [selectedStaffType, setSelectedStaffType] = useState<number>(0);

  const { data: staffTypes } = useStaffType();

  const { data } = useGet({
    url: '/new/stats',
    params: {
      fromDateOffset: fromDate ? differenceInDays(new Date(), fromDate) : 0,
      toDateOffset: toDate ? differenceInDays(new Date(), toDate) : undefined,
    },
  });

  useEffect(() => {
    if (staffTypes) setSelectedStaffType(staffTypes[0].id);
  }, [staffTypes]);

  useEffect(() => {
    if (data && data.length > 0) {
      const _data: {
        id: number;
        name: string;
        staffTypeId: number;
        totalCustomersReq: number;
        totalRealTurn: number;
        totalSpecTurn: number;
        totalTurn: number;
        total: number;
      }[] = data;

      const _statsData: StatsData[] = _data.map((el) => {
        return {
          turn: el.totalTurn,
          cusReq: el.totalCustomersReq,
          realTurn: el.totalRealTurn,
          specTurn: el.totalSpecTurn,
          total: el.total,
          staff: {
            id: el.id,
            name: el.name,
            staffTypeId: el.staffTypeId,
          },
        };
      });

      setStatsData(_statsData);
    }
  }, [data]);

  const handleSort = (sortBy: SortBy, isAsc: boolean) => {
    const asc = isAsc ? 1 : -1;
    const sortFn = (a: StatsData, b: StatsData) => {
      if (sortBy == SortBy.NAME) return asc * b.staff.name.localeCompare(a.staff.name);

      if (sortBy == SortBy.ID) return asc * (a.staff.id - b.staff.id);
      if (sortBy == SortBy.TURN) return asc * (a.turn - b.turn);
      if (sortBy == SortBy.REAL_TURN) return asc * (a.realTurn - b.realTurn);
      if (sortBy == SortBy.REQ_TURN) return asc * (a.cusReq - b.cusReq);
      if (sortBy == SortBy.SPEC_TURN) return asc * (a.specTurn - b.specTurn);

      return asc * (a.total - b.total);
    };

    const _statsData = [...statsData];
    _statsData.sort(sortFn);
    setStatsData(_statsData);
  };

  return (
    <div className="mt-10">
      <h3 className="uppercase text-center mb-4 text-2xl font-extrabold leading-none tracking-tight text-gray-900 md:text-3xl lg:text-4xl dark:text-white">
        Thống kê turn {toDate && 'từ'} ngày {fromDate && format(fromDate, 'dd/MM/yyyy')}
        {toDate && ` đến ${format(toDate, 'dd/MM/yyyy')}`}
      </h3>

      <div className="flex justify-between items-center">
        <div>
          <Tabs
            value={selectedStaffType.toString()}
            onValueChange={(value) => setSelectedStaffType(Number(value))}
          >
            <TabsList>
              {(staffTypes ?? []).map((el) => (
                <TabsTrigger key={el.id} value={el.id.toString()}>
                  {el.name}
                </TabsTrigger>
              ))}
            </TabsList>
          </Tabs>
        </div>
        <div className="flex gap-2 items-center">
          <Popover open={fromCalendarOpen} onOpenChange={setFromCalendarOpen}>
            <PopoverTrigger asChild>
              <Button
                variant={'outline'}
                className={cn(
                  'w-[140px] justify-start text-left font-normal',
                  !fromDate && 'text-muted-foreground',
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {fromDate ? format(fromDate, 'dd/MM/yyyy') : 'Chọn ngày'}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={fromDate}
                defaultMonth={fromDate}
                disabled={(date) => date > new Date()}
                onSelect={(date) => {
                  setFromDate(date);
                  setFromCalendarOpen(false);
                }}
                fromYear={2024}
                toDate={new Date()}
                captionLayout="dropdown-buttons"
                components={{
                  Dropdown: (props) => (
                    <select
                      {...props}
                      className="px-2 py-1 mx-2 text-sm border rounded-md bg-white dark:bg-black"
                    />
                  ),
                }}
                classNames={{
                  nav_button_previous: '',
                  nav_button_next: '',
                }}
              />
            </PopoverContent>
          </Popover>
          <ArrowBigRight />
          <Popover open={toCalendarOpen} onOpenChange={setToCalendarOpen}>
            <PopoverTrigger asChild>
              <Button
                variant={'outline'}
                className={cn(
                  'w-[140px] justify-start text-left font-normal',
                  !toDate && 'text-muted-foreground',
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {toDate ? format(toDate, 'dd/MM/yyyy') : 'Chọn ngày'}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={toDate}
                defaultMonth={toDate}
                captionLayout="dropdown-buttons"
                disabled={(date) => date > new Date()}
                onSelect={(date) => {
                  setToDate(date);
                  setToCalendarOpen(false);
                }}
                fromYear={2024}
                toDate={new Date()}
                components={{
                  Dropdown: (props) => (
                    <select
                      {...props}
                      className="px-2 py-1 mx-2 text-sm border rounded-md bg-white dark:bg-black"
                    />
                  ),
                }}
                classNames={{
                  nav_button_previous: '',
                  nav_button_next: '',
                }}
              />
            </PopoverContent>
          </Popover>
        </div>
      </div>

      <div className="">
        <Table className="">
          <TableHeader>
            <HeaderRow handleSort={handleSort} />
          </TableHeader>
          <TableBody className="overflow-auto">
            {statsData
              .filter((el) => el.staff.staffTypeId == selectedStaffType)
              .map((el, index) => (
                <TableRow key={el.staff.id}>
                  <TableCell className="font-medium text-center">{index + 1}</TableCell>
                  <TableCell>{el.staff.name}</TableCell>
                  <TableCell className="text-center">{el.turn}</TableCell>
                  <TableCell className="text-center">{el.realTurn}</TableCell>
                  <TableCell className="text-center">{el.cusReq}</TableCell>
                  <TableCell className="text-center">{el.specTurn}</TableCell>
                  <TableCell className="text-center">{el.total}</TableCell>
                </TableRow>
              ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}

type StatsData = {
  staff: IStaff;
  realTurn: number;
  turn: number;
  cusReq: number;
  specTurn: number;
  total: number;
};

enum SortBy {
  NAME,
  ID,
  TURN,
  REAL_TURN,
  SPEC_TURN,
  REQ_TURN,
  ALL_TURN,
}

function HeaderRow({ handleSort }: { handleSort: (sortBy: SortBy, isAsc: boolean) => void }) {
  const [isAsc, setAsc] = useState(false);
  const [sortBy, setSortBy] = useState<SortBy>(SortBy.TURN);

  const handleCLick = (newSortBy: SortBy) => {
    let _isAsc = true;

    if (sortBy == newSortBy) {
      _isAsc = !isAsc;
    } else {
      setSortBy(newSortBy);
    }

    if (_isAsc != isAsc) setAsc(_isAsc);

    handleSort(newSortBy, _isAsc);
  };

  return (
    <TableRow>
      <TableHead className="w-[100px] text-center">STT</TableHead>
      <TableHead>
        <Button
          onClick={() => handleCLick(SortBy.NAME)}
          variant={'ghost'}
          className="w-full flex items-center "
        >
          Họ và tên
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      </TableHead>
      <TableHead className="text-center">
        <Button
          onClick={() => handleCLick(SortBy.TURN)}
          variant={'ghost'}
          className="w-full flex items-center relative"
        >
          Số turn thực tế
          <ArrowUpDown className="ml-2 h-4 w-4" />
          <div className="text-xs absolute top-full left-1/2 transform -translate-x-1/2">
            Bao gồm turn chuyển từ khách đòi
          </div>
        </Button>
      </TableHead>
      <TableHead className="text-center">
        <Button
          onClick={() => handleCLick(SortBy.REAL_TURN)}
          variant={'ghost'}
          className="w-full flex items-center"
        >
          Số turn
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      </TableHead>
      <TableHead className="text-center">
        <Button
          onClick={() => handleCLick(SortBy.REQ_TURN)}
          variant={'ghost'}
          className="w-full flex items-center"
        >
          Số khách đòi
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      </TableHead>
      <TableHead className="text-center">
        <Button
          onClick={() => handleCLick(SortBy.SPEC_TURN)}
          variant={'ghost'}
          className="w-full flex items-center"
        >
          Số turn cuối
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      </TableHead>
      <TableHead className="text-center">
        <Button
          onClick={() => handleCLick(SortBy.ALL_TURN)}
          variant={'ghost'}
          className="w-full flex items-center relative"
        >
          Tổng khách
          <ArrowUpDown className="ml-2 h-4 w-4" />
          <div className="text-xs absolute top-full left-1/2 transform -translate-x-1/2">
            Số turn + Số khách đòi
          </div>
        </Button>
      </TableHead>
    </TableRow>
  );
}
