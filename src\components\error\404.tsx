import { Link } from "react-router-dom";

export default function NotFound() {
  return (
    <div className="grow h-full w-full flex flex-col justify-center items-center">
      <h1 className="mb-6 text-6xl font-extrabold leading-none tracking-tight text-gray-900 md:text-7xl lg:text-9xl dark:text-white">
        Oops!
      </h1>

      <h3 className="mb-4 text-2xl font-extrabold leading-none tracking-tight text-gray-900 md:text-3xl lg:text-4xl dark:text-white">
        404 - PAGE NOT FOUND
      </h3>

      <p className="mb-6 text-lg font-normal text-gray-500 lg:text-xl sm:px-16 xl:px-48 dark:text-gray-400">
        The page you are looking for doesn't exist
      </p>

      <Link to="/">
        <a
          href="#"
          className="inline-flex items-center justify-center px-5 py-3 text-base font-medium text-center text-white bg-blue-700 rounded-lg hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 dark:focus:ring-blue-900"
        >
          Go Home
          <svg
            className="w-3.5 h-3.5 ms-2 rtl:rotate-180"
            aria-hidden="true"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 14 10"
          >
            <path
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M1 5h12m0 0L9 1m4 4L9 9"
            />
          </svg>
        </a>
      </Link>
    </div>
  );
}
