import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableCell,
  TableBody,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { differenceInDays } from "date-fns";
import { useEffect, useState } from "react";
import { TransactionType } from "./create-transaction";
import { useGet } from "@/hooks/useGet";
import { Pagination } from "@/components/pagination";
import { ArrowDown, ArrowUp } from "lucide-react";

type ITransactionStaff = {
  transactionType: TransactionType;
  totalQuantity: number;
  staffName?: string;
};

type HistoryStaffProps = {
  enable: boolean;
  product: IProduct;
  fromDate: Date | undefined;
  toDate: Date | undefined;
};

export function HistoryStaff({
  enable,
  product,
  fromDate,
  toDate,
}: HistoryStaffProps) {
  const [staffName, setStaffName] = useState("");
  const [debouncedStaffName, setDebouncedStaffName] = useState("");

  const [transactionType, setTransactionType] = useState(
    TransactionType.EXPORT
  );

  const [data, setData] = useState<ITransactionStaff[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const [asc, setAsc] = useState(false);

  console.log("asc", asc);

  const { data: rawData } = useGet({
    enabled: enable,
    url: "/inventory/transaction/staff",
    params: {
      productId: product.id,
      fromDateOffset: fromDate ? differenceInDays(new Date(), fromDate) : 0,
      toDateOffset: toDate ? differenceInDays(new Date(), toDate) : undefined,
      transactionType,
      staffName:
        debouncedStaffName.trim() === "" ? undefined : debouncedStaffName,
      page: currentPage,
      asc: asc,
    },
  });

  useEffect(() => {
    if (rawData) {
      setData(rawData.data);
      setTotalPages(rawData.totalPages);
    }
  }, [rawData]);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedStaffName(staffName);
    }, 300);

    return () => {
      clearTimeout(handler);
    };
  }, [staffName]);

  return (
    <div className="">
      <div className="">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[100px]">STT</TableHead>
              <TableHead className="w-[240px]">
                <div className="flex flex-row gap-4 items-center">
                  <span>Loại</span>
                  <Select
                    value={transactionType}
                    onValueChange={(value) =>
                      setTransactionType(value as TransactionType)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={TransactionType.IMPORT}>
                        Nhập
                      </SelectItem>
                      <SelectItem value={TransactionType.EXPORT}>
                        Xuất
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </TableHead>
              <TableHead className="">
                <div className="flex flex-row items-center gap-4">
                  <span>Tên nhân viên</span>
                  <Input
                    value={staffName}
                    onChange={(event) => setStaffName(event.target.value)}
                    placeholder="Tìm tên"
                    className="max-w-[200px]"
                  />
                </div>
              </TableHead>
              <TableHead
                className="w-[150px]"
                onClick={() => setAsc((asc) => !asc)}
              >
                <div className="flex items-center gap-1">
                  Tổng Số
                  {asc ? (
                    <ArrowUp className="h-4 w-4" />
                  ) : (
                    <ArrowDown className="h-4 w-4" />
                  )}
                </div>
              </TableHead>
              <TableHead className="w-[150px]">Đơn vị</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.map((el, index) => (
              <TableRow key={index}>
                <TableCell className="font-medium">{index + 1}</TableCell>
                <TableCell>
                  {el.transactionType == TransactionType.IMPORT
                    ? "Nhập hàng"
                    : "Xuất hàng"}
                </TableCell>
                <TableCell>{el.staffName || "Manager"}</TableCell>
                <TableCell>{el.totalQuantity}</TableCell>
                <TableCell>{product.unit}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={(page) => setCurrentPage(page)}
      />
    </div>
  );
}
