import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  Di<PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { But<PERSON> } from "@/components/ui/button";

import { usePost } from "@/hooks/usePost";
import { ReactNode, useEffect, useState } from "react";

import { Loader2, Plus } from "lucide-react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { useToast } from "@/components/ui/use-toast";

type ComponentProps = {
  productTypes: IProductType[];
  refetch: () => void;
  tab: string;
  renderButton?: ReactNode;
  unitFilter?: string;
};

export function AddProduct({
  renderButton,
  productTypes,
  refetch,
  tab,
  unitFilter = "-1",
}: ComponentProps) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  const [fullname, setFullname] = useState("");
  const [unit, setUnit] = useState("");
  const [warningQuantity, setWarningQuantity] = useState(0);
  const [productTypeId, setProductTypeId] = useState<number>();

  const { toast } = useToast();

  const { mutate } = usePost({
    url: "/inventory/product/create",
    onSuccess: () => {
      setLoading(false);
      refetch();
      setOpen(false);
    },
    onError: () => {
      toast({
        variant: "destructive",
        description: "Có lỗi xảy ra. Vui lòng reload lại trang.",
      });
      setLoading(false);
    },
  });

  useEffect(() => {
    setUnit(unitFilter == "-1" ? "" : unitFilter);
    setFullname("");
    setWarningQuantity(0);
  }, [open]);

  useEffect(() => {
    setProductTypeId(parseInt(tab));
  }, [tab]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {renderButton || (
          <Button className="my-4 mx-1" variant="default" size="sm">
            <Plus className="mr-2 h-4 w-4" /> Thêm sản phẩm
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Thêm sản phẩm</DialogTitle>
        </DialogHeader>
        <DialogDescription></DialogDescription>

        <div className="">
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">Tên sản phẩm</Label>
              <Input
                className="col-span-3"
                value={fullname}
                onChange={(event) => {
                  setFullname(event.target.value);
                }}
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">Đơn vị</Label>
              <Input
                className="col-span-3"
                value={unit}
                onChange={(event) => {
                  setUnit(event.target.value);
                }}
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">Số lượng cảnh báo</Label>
              <Input
                className="col-span-3"
                value={Number(warningQuantity).toString()}
                onChange={(event) => {
                  setWarningQuantity(parseInt(event.target.value));
                }}
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">Dịch vụ</Label>
              <Select
                value={productTypeId?.toString()}
                onValueChange={(value) => setProductTypeId(parseInt(value))}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {productTypes.map((el) => (
                    <SelectItem key={el.id} value={el.id.toString()}>
                      {el.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button
            disabled={loading}
            variant="destructive"
            onClick={() => {
              setLoading(true);
              mutate({
                name: fullname,
                unit,
                productTypeId,
                warningQuantity,
              });
            }}
          >
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Thêm sản phẩm
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
