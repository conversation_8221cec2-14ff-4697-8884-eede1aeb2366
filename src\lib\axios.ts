import axios from 'axios';
import { API_URL } from './constants';

const client = axios.create({
  baseURL: API_URL,
});

export type RequestProps = {
  url: string;
  method: string;
  params?: object;
  data?: object;
  jwt?: string | null;
};

export const request = async (options: RequestProps) => {
  if (options.jwt) {
    client.defaults.headers.common.Authorization = `Bearer ${options.jwt}`;
  } else {
    const accessToken = localStorage.getItem('access-token');
    if (accessToken) client.defaults.headers.common.Authorization = `Bearer ${accessToken}`;
  }

  // eslint-disable-next-line
  const onSuccess = (response: any) => {
    // console.log("response", response?.data);
    return response?.data;
  };

  // eslint-disable-next-line
  const onError = (error: any) => {
    console.log(error);
    return Promise.reject(error.response?.data);
  };

  return client(options).then(onSuccess).catch(onError);
};
