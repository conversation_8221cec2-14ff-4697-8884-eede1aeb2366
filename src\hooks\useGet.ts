import { useQuery } from '@tanstack/react-query';
import { request } from '../lib/axios';
import { useShop } from './useShop';

type GetData = {
  url: string;
  isShop?: boolean;
  key?: string[];
  params?: object;
  data?: object;
  onSuccess?: () => void;
  onError?: () => void;
  enabled?: boolean;
  jwt?: string;
};

async function getFn({ url, params, onSuccess, onError, jwt }: GetData) {
  try {
    const data = await request({ url, method: 'GET', params, jwt });
    if (onSuccess) onSuccess();
    return data;
  } catch (err) {
    if (onError) onError();
  }
}

export const useGet = ({ isShop = true, ...data }: GetData) => {
  const { url, params } = data;
  const { shop } = useShop();

  let newUrl = url;

  if (isShop) {
    const base = shop?.shopUrl?.replace(/\/+$/, ''); // bỏ / cuối
    const path = url.replace(/^\/+/, ''); // bỏ / đầu
    newUrl = `${base}/api/${path}`;
  }

  let queryKey = url.split('/').filter((el) => el != '');
  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      queryKey = [...queryKey, key, value];
    });
  }

  const fullQueryKey = [...(data.key || queryKey), isShop ? shop?.id || shop?.shopUrl : undefined];

  return useQuery({
    queryKey: fullQueryKey,
    queryFn: () => getFn({ ...data, url: newUrl, jwt: isShop ? shop?.jwt : undefined }),
    enabled: data.enabled,
  });
};
