import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useToast } from "@/components/ui/use-toast";

import { usePost } from "@/hooks/usePost";

import { Loader2 } from "lucide-react";
import { useState } from "react";

type ComponentProps = {
  refetch: () => void;
  staff: IStaff;
};

export function SkipWork({ refetch, staff }: ComponentProps) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  const { toast } = useToast();

  const { mutate } = usePost({
    url: "/work/skip",
    onSuccess: () => {
      setLoading(false);
      refetch();
      setOpen(false);
    },
    onError: () => {
      toast({
        variant: "destructive",
        description: "Có lỗi xảy ra. Vui lòng reload lại trang.",
      });
      setLoading(false);
    },
  });

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button size="sm" variant="destructive" className="m-1">
          Bỏ qua
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Bỏ qua turn</DialogTitle>
        </DialogHeader>
        <DialogDescription></DialogDescription>

        <div className="">
          Bạn có chắc chắn muốn <b>{` BỎ QUA `}</b> turn hiện tại của
          <b>{` ${staff.name} `}</b>không?
        </div>
        <DialogFooter>
          <Button
            variant="secondary"
            disabled={loading}
            onClick={() => {
              setOpen(false);
            }}
          >
            Không
          </Button>

          <Button
            disabled={loading}
            variant="destructive"
            onClick={() => {
              if (staff?.id) {
                setLoading(true);
                mutate({
                  staffId: staff?.id,
                });
              }
            }}
          >
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Có, bỏ qua
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
