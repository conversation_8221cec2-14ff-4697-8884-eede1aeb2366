import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useToast } from "@/components/ui/use-toast";

import { usePost } from "@/hooks/usePost";

import { Loader2 } from "lucide-react";
import { useState } from "react";

type ComponentProps = {
  refetch: () => void;
  staff: IStaff;
};

export function ComebackWork({ refetch, staff }: ComponentProps) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  const { toast } = useToast();

  const { mutate } = usePost({
    url: `/staff/comeback`,
    onSuccess: () => {
      setLoading(false);
      refetch();
      setOpen(false);
    },
    onError: () => {
      toast({
        variant: "destructive",
        description: "Có lỗi xảy ra. Vui lòng reload lại trang.",
      });
      setLoading(false);
    },
  });

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button size="sm" variant="destructive" className="m-1">
          Off
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Quay lại làm việc</DialogTitle>
        </DialogHeader>
        <DialogDescription></DialogDescription>

        <div className="">
          Bạn có chắc nhân viên <b>{` ${staff.name} `}</b> sẽ <b>QUAY LẠI</b>{" "}
          làm việc?
        </div>
        <DialogFooter>
          <Button
            variant="secondary"
            disabled={loading}
            onClick={() => {
              setOpen(false);
            }}
          >
            Không
          </Button>

          <Button
            disabled={loading}
            variant="default"
            onClick={() => {
              if (staff?.id) {
                setLoading(true);
                mutate({ id: staff?.id });
              }
            }}
          >
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Có, quay lại
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
