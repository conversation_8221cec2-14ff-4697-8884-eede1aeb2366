import { DndProvider } from 'react-dnd';
import { Toaster } from './components/ui/toaster';
import { AuthProvider } from './providers/auth-provider';
import { RouteProvider } from './providers/route-provider';
import { ThemeProvider } from './providers/theme-provider';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { useEffect } from 'react';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      // refetchOnMount: false,
      // refetchOnReconnect: false,
    },
  },
});

function App() {
  useEffect(() => {
    document.title = import.meta.env.VITE_TITLE;
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider>
        <AuthProvider>
          <DndProvider backend={HTML5Backend}>
            <RouteProvider />
            <Toaster />
          </DndProvider>
        </AuthProvider>
      </ThemeProvider>
    </QueryClientProvider>
  );
}

export default App;
