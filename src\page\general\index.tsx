import { Table, TableBody, TableHead, TableHeader, TableRow } from '@/components/ui/table';

import { useStaffDate } from '@/hooks/useStaff';

import { Loading } from '@/components/loading';
import { useEffect, useState } from 'react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { CalendarIcon } from 'lucide-react';
import { format } from 'date-fns';
import { Calendar } from '@/components/ui/calendar';
import { calTurn, cn } from '@/lib/utils';
import { StaffRow } from './staff-row';
import { usePost } from '@/hooks/usePost';
import { toast } from '@/components/ui/use-toast';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { DataProvider, useData } from './data-provider';
import BusyHistory from './crud/busy-history';

export function General() {
  return (
    <DataProvider>
      <GeneralWrapper />
    </DataProvider>
  );
}

function GeneralWrapper() {
  const [open, setOpen] = useState(false);
  const [tab, setTab] = useState(0);

  const { date, setDate, staffTypes } = useData();

  useEffect(() => {
    if (staffTypes.length > 0 && !tab) {
      setTab(staffTypes[0].id);
    }
  }, [staffTypes]);

  return (
    <div className="mt-10">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <Tabs value={tab.toString()} onValueChange={(value) => setTab(parseInt(value))}>
            <div className="flex justify-between items-center">
              <TabsList>
                {staffTypes.map((el) => (
                  <TabsTrigger key={el.id} value={el.id.toString()}>
                    {el.name}
                  </TabsTrigger>
                ))}
              </TabsList>
            </div>
          </Tabs>

          <BusyHistory />
        </div>
        <div className="flex items-center">
          <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
              <Button
                variant={'outline'}
                className={cn(
                  'w-[280px] justify-start text-left font-normal',
                  !date && 'text-muted-foreground',
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {format(date, 'yyyy/MM/dd')}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={date}
                defaultMonth={date}
                disabled={(date) => date > new Date()}
                onSelect={(date) => {
                  setDate(date || new Date());
                  setOpen(false);
                }}
              />
            </PopoverContent>
          </Popover>
        </div>
      </div>

      <StaffData tab={tab} />
    </div>
  );
}

function filter(staffs: IStaff[], staffTypeId: number) {
  return staffs.filter((staff: IStaff) => staff.staffTypeId == staffTypeId);
}

function StaffData({ tab }: { tab: number }) {
  const { settingData, setOrders, date, staffs, setStaffs } = useData();
  const { isPending, data, refetch } = useStaffDate(date);

  const { mutate } = usePost({
    url: `/staff/sort`,
    onError: () => {
      toast({
        variant: 'destructive',
        description: 'Có lỗi xảy ra. Vui lòng reload lại trang.',
      });
    },
  });

  useEffect(() => {
    if (data) {
      setStaffs(data);
    }
  }, [data]);

  useEffect(() => {
    if (staffs?.length > 0 && tab) {
      const _orders = [];

      const _staffs = filter(staffs, tab).sort(
        (a, b) => calTurn(a, settingData) - calTurn(b, settingData),
      );

      for (const staff of _staffs) {
        if (staff.today?.onWorking) continue;
        if (staff.today?.isBusy) continue;
        if (staff.today?.isLeave) continue;

        _orders.push(staff.id);
      }

      setOrders(_orders);
    }
  }, [staffs, tab]);

  const reorderRow = (draggedRowId: number, targetRowId: number) => {
    const draggedRowIndex = staffs.findIndex((el) => el.id == draggedRowId);
    const targetRowIndex = staffs.findIndex((el) => el.id == targetRowId);

    const _staffs = [...staffs];
    _staffs.splice(targetRowIndex, 0, _staffs.splice(draggedRowIndex, 1)[0]);

    mutate({ orders: _staffs.map((el) => el.id) });
    setStaffs(_staffs);
  };

  if (isPending) return <Loading />;

  return (
    <div className="overflow-auto">
      <Table>
        <TableHeader>
          <HeaderRow />
        </TableHeader>
        <TableBody>
          {filter(staffs, tab).map((staff: IStaff, index) => (
            <StaffRow
              key={staff.id}
              rawStaff={staff}
              staffIndex={index + 1}
              reorderRow={reorderRow}
              refetch={refetch}
            />
          ))}
        </TableBody>
      </Table>
    </div>
  );
}

function HeaderRow() {
  const { isEditable } = useData();

  return (
    <TableRow>
      {/* <TableHead className="w-[0px]"></TableHead> */}
      {isEditable && <TableHead className="text-center w-[50px]"></TableHead>}
      <TableHead className="w-0">STT</TableHead>
      <TableHead className="text-left w-0">Họ và tên</TableHead>
      {isEditable && <TableHead className="text-center w-0">Thứ tự </TableHead>}
      {isEditable && <TableHead className="text-center w-[50px]">Hành động</TableHead>}
      <TableHead className="text-center w-0">Tổng khách </TableHead>
      <TableHead className="text-center w-0">Số Turn </TableHead>
      <TableHead className="text-center w-0">Khách đòi</TableHead>
      <TableHead className="text-center w-0">Turn cuối</TableHead>
      <TableHead className="text-center w-0">Tình trạng</TableHead>
      <TableHead>Công việc</TableHead>
    </TableRow>
  );
}

// function BannerRow({
//   description,
//   length,
// }: {
//   description: string;
//   length: number;
// }) {
//   return (
//     <TableRow>
//       <TableCell rowSpan={length + 1} className="">
//         <h5
//           className="text-sm rotate-180"
//           style={{ writingMode: "vertical-rl" }}
//         >
//           {description}
//         </h5>
//       </TableCell>
//     </TableRow>
//   );
// }
