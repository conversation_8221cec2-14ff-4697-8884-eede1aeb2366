import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDes<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";

import { usePost } from "@/hooks/usePost";
import { useEffect, useState } from "react";

import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";

import { Loader2 } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";

type ComponentProps = {
  product: IProduct | null;
  productTypes: IProductType[];
  refetch: () => void;
};

export function EditProduct({
  product,
  productTypes,
  refetch,
}: ComponentProps) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  const { toast } = useToast();

  const [fullname, setFullname] = useState("");
  const [unit, setUnit] = useState("");
  const [warningQuantity, setWarningQuantity] = useState(0);
  const [productTypeId, setProductTypeId] = useState<number>();

  const { mutate } = usePost({
    url: "/inventory/product/update",
    onSuccess: () => {
      setLoading(false);
      refetch();
      setOpen(false);
    },
    onError: () => {
      toast({
        variant: "destructive",
        description: "Có lỗi xảy ra. Vui lòng reload lại trang.",
      });
      setLoading(false);
    },
  });

  useEffect(() => {
    if (product) {
      setFullname(product.name);
      setUnit(product.unit);
      setWarningQuantity(product.warningQuantity);
      setProductTypeId(product.productTypeId);
    }
  }, [open, product]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="secondary" className="m-1">
          Sửa
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Sửa sản phẩm</DialogTitle>
        </DialogHeader>
        <DialogDescription></DialogDescription>

        <div className="">
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">Tên sản phẩm</Label>
              <Input
                className="col-span-3"
                value={fullname}
                onChange={(event) => {
                  setFullname(event.target.value);
                }}
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">Đơn vị</Label>
              <Input
                className="col-span-3"
                value={unit}
                onChange={(event) => {
                  setUnit(event.target.value);
                }}
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">Số lượng cảnh báo</Label>
              <Input
                className="col-span-3"
                value={Number(warningQuantity).toString()}
                onChange={(event) => {
                  setWarningQuantity(parseInt(event.target.value));
                }}
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">Dịch vụ</Label>
              <Select
                disabled
                value={productTypeId?.toString()}
                onValueChange={(value) => setProductTypeId(parseInt(value))}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {productTypes.map((el) => (
                    <SelectItem key={el.id} value={el.id.toString()}>
                      {el.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button
            disabled={loading}
            variant="destructive"
            onClick={() => {
              if (product?.id) {
                setLoading(true);
                mutate({
                  id: product?.id,
                  name: fullname,
                  unit,
                  warningQuantity,
                });
              }
            }}
          >
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Sửa sản phẩm
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
