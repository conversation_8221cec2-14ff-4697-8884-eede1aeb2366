import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON>alog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

import { usePost } from "@/hooks/usePost";
import { useState } from "react";

import { Loader2 } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";

type ComponentProps = {
  unlock: boolean;
  setUnlock: (arg: boolean) => void;
};

export function UnlockDialog({ unlock, setUnlock }: ComponentProps) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [password, setPassword] = useState("");

  const { toast } = useToast();

  const { mutate } = usePost({
    url: `/auth/sign-in`,
    onSuccess: () => {
      setPassword("");
      setUnlock(true);
      setLoading(false);
      setOpen(false);
    },
    onError: () => {
      toast({
        variant: "destructive",
        description: "Sai mật khẩu.",
      });
      setLoading(false);
    },
  });

  return (
    <>
      <Button
        variant="default"
        onClick={() => {
          if (unlock) {
            setUnlock(false);
          } else {
            setOpen(true);
          }
        }}
      >
        {unlock ? "Ẩn chỉ tiết" : "Mở khoá"}
      </Button>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Mở khoá</DialogTitle>
          </DialogHeader>
          <DialogDescription></DialogDescription>

          <div>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label className="text-right">Mật khẩu</Label>
                <Input
                  type="password"
                  className="col-span-3"
                  value={password}
                  onChange={(event) => setPassword(event.target.value)}
                />
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button
              disabled={loading}
              onClick={() => {
                setLoading(true);
                mutate({ username: "admin", password });
              }}
            >
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Mở khoá
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
