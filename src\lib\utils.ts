import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";
import { differenceInSeconds, format } from "date-fns";
import { START_NEW_RULE_DATE, VITE_NEW_RULE } from "./constants";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatPrice(price: number) {
  const prefix = price / 100;
  const postfix = price % 100;
  return `$${prefix}.${postfix}`;
}

export function formatNumberWith0(s: number, l: number) {
  return s.toLocaleString("en-US", {
    minimumIntegerDigits: l,
    useGrouping: false,
  });
}

export function formatID(id: number) {
  return formatNumberWith0(id, 3);
}

export function today() {
  const date = new Date();
  return format(date, "yyyy/MM/dd");
}

export function formatTimeClock(date: Date) {
  return format(date, "HH:mm:ss");
}

export function secondsToTime(seconds: number, withHours = true) {
  const h = withHours ? Math.floor(seconds / 3600) : 9;
  const remain = withHours ? seconds % 3600 : seconds;
  const m = Math.floor(remain / 60);
  const s = remain % 60;

  return { hours: h, minutes: m, seconds: s, withHours };
}

type TIME_TYPE = {
  hours: number;
  minutes: number;
  seconds: number;
  withHours: boolean;
};

export function formatTime({ hours, minutes, seconds, withHours }: TIME_TYPE) {
  const m = minutes < 10 ? `0${minutes}` : `${minutes}`;
  const s = seconds < 10 ? `0${seconds}` : `${seconds}`;

  if (withHours) return `${hours}:${m}:${s}`;
  return `${m}:${s}`;
}

export function formatTimeWithText({
  hours,
  minutes,
  seconds,
  withHours,
}: TIME_TYPE) {
  let str = "";
  if (withHours && hours > 0) str += hours + " giờ ";
  if (minutes > 0 || hours > 0) str += minutes + " phút ";
  if (seconds > 0) str += seconds + " giây ";
  return str;
}

export function tasksPrice(tasks: ITask[]) {
  return tasks.reduce((taskTotal, task) => taskTotal + task.price, 0);
}

export function worksPrice(works: IWork[]) {
  return works.reduce((total, work) => total + tasksPrice(work.tasks || []), 0);
}

export function tasksTime(tasks: ITask[]) {
  return tasks.reduce((taskTotal, task) => taskTotal + task.time, 0);
}

export function worksTime(works: IWork[]) {
  return works.reduce((total, work) => total + tasksTime(work.tasks || []), 0);
}

export function realWorksTime(works: IWork[]) {
  return works.reduce((total, work) => total + (work.time || 0), 0);
}

export function colorWork(work: IWork) {
  if (work.isSkip) return "bg-yellow-500 hover:bg-yellow-500 text-white";

  if (!work.time || work.onWorking)
    return "bg-blue-700 hover:bg-blue-700 text-white";

  if (work.customerRequest)
    return "bg-purple-500 hover:bg-purple-500 text-white";

  const time = tasksTime(work.tasks || []);
  if (time > work.time) return "bg-red-700 hover:bg-red-700 text-white";

  return "bg-green-700 hover:bg-green-700 text-white";
}

export function labelCurrentStatus(
  info: IInfo,
  time: number,
  isEditable: boolean
) {
  if (info.isLeave) return "Off";
  if (info.isBusy) return "Bận rộn";
  if (info.onWorking) {
    const _current = new Date(time);
    const _startOn = info?.startOn ? new Date(info?.startOn) : new Date();
    if (differenceInSeconds(_startOn, _current) > 0) return "Chuẩn bị";
    return "Làm việc";
  }

  if (!isEditable) return "Hoàn thành";
  return "Chờ khách";
}

export function canWorking(today: IInfo | undefined) {
  if (today?.isBusy) return false;
  if (today?.isLeave) return false;
  if (today?.onWorking) return false;

  return true;
}

export function isApplyNewRule(date: Date, staffTypeId: number) {
  if (!VITE_NEW_RULE) return false;

  // Only apply new rule in 2 -> 5
  const _is2To5 =
    date.getDay() != 5 && date.getDay() != 6 && date.getDay() != 0;

  // Only apply new rule after 30/09/2024
  const _isValidDate = date.getTime() > START_NEW_RULE_DATE.getTime();

  // Only apply new rule for Nails
  const _isNails = staffTypeId == 1;

  return _isNails && _isValidDate && _is2To5;
}

export function totalCustomers(
  staff: IStaff | undefined,
  settingData: ISettingData
) {
  if (!staff?.today) return 0;

  const _turn = calRealTurn(staff, settingData);
  const _cusReq = staff.today.customersReq || 0;

  return _turn + _cusReq;
}

export function isApplyRule(staffTypeId: number, settingData: ISettingData) {
  if (settingData.convertCusReq > 0) {
    if (settingData.isDateApplyRule) {
      const _isApplyStaffType =
        settingData.applyRuleStaffType.filter((el) => el == staffTypeId)
          .length > 0;

      if (_isApplyStaffType) {
        return true;
      }
    }
  }

  return false;
}

export function calTurn(
  staff: IStaff | undefined,
  settingData: ISettingData,
  info?: IInfo
) {
  if (!staff) return 0;
  const _info = info || staff?.today;
  if (!_info) return 0;

  if (_info.realTurn) {
    const _isApplyRule = isApplyRule(staff.staffTypeId, settingData);

    let _incTurn = 0;
    if (_isApplyRule) {
      const _cusReq = _info.customersReq || 0;
      _incTurn = Math.floor(_cusReq / settingData.convertCusReq);
    }

    return _info.realTurn + _incTurn;
  }

  return _info.turn || 0;
}

export function calRealTurn(
  staff: IStaff | undefined,
  settingData: ISettingData,
  info?: IInfo
) {
  if (!staff) return 0;
  const _info = info || staff?.today;
  if (!_info) return 0;

  if (_info.realTurn && _info.realTurn != 0) {
    return _info.realTurn;
  }

  const _isApplyRule = isApplyRule(staff.staffTypeId, settingData);

  let _incTurn = 0;
  if (_isApplyRule) {
    const _cusReq = _info.customersReq || 0;
    _incTurn = Math.floor(_cusReq / settingData.convertCusReq);
  }

  return (_info.turn || 0) - _incTurn;
}
