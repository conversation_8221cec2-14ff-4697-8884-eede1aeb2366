import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useToast } from "@/components/ui/use-toast";

import { usePost } from "@/hooks/usePost";
import { differenceInSeconds } from "date-fns";

import { Loader2 } from "lucide-react";
import { useEffect, useState } from "react";

type ComponentProps = {
  refetch: () => void;
  staff: IStaff;
};

export function EndWork({ refetch, staff }: ComponentProps) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [time, setTime] = useState(Math.floor(Date.now()));

  const { toast } = useToast();

  useEffect(() => {
    const intervalId = setInterval(() => {
      setTime(Math.floor(Date.now()));
    }, 1000);

    return () => clearInterval(intervalId);
  }, []);

  const { mutate } = usePost({
    url: "/work/end",
    onSuccess: () => {
      setLoading(false);
      refetch();
      setOpen(false);
    },
    onError: () => {
      toast({
        variant: "destructive",
        description: "Có lỗi xảy ra. Vui lòng reload lại trang.",
      });
      setLoading(false);
    },
  });

  const _current = new Date(time);
  const _startOn = staff.today?.startOn
    ? new Date(staff.today?.startOn)
    : new Date();

  const isNotReady = differenceInSeconds(_startOn, _current) > 0;

  return (
    <>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger asChild>
          <Button
            size="sm"
            variant="default"
            className="m-1 bg-orange-500 text-white hover:bg-orange-500"
          >
            {isNotReady ? "Xoá" : "Hoàn thành"}
          </Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>
              {isNotReady ? "Xoá chuẩn bị" : "Hoàn thành công việc"}
            </DialogTitle>
          </DialogHeader>
          <DialogDescription></DialogDescription>

          {isNotReady ? (
            <div className="">
              Bạn có chắc chắn muốn <b>{` XOÁ CHUẨN BỊ `}</b> công việc hiện tại
              của
              <b>{` ${staff.name} `}</b>không?
            </div>
          ) : (
            <div className="">
              Bạn có chắc chắn muốn <b>{` HOÀN THÀNH `}</b> công việc hiện tại
              của
              <b>{` ${staff.name} `}</b>không?
            </div>
          )}

          <DialogFooter>
            <Button
              variant="secondary"
              disabled={loading}
              onClick={() => {
                setOpen(false);
              }}
            >
              Không
            </Button>

            <Button
              disabled={loading}
              variant="default"
              onClick={() => {
                if (staff?.id) {
                  setLoading(true);
                  mutate({ staffId: staff?.id });
                }
              }}
            >
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isNotReady ? "Có, xoá chuẩn bị" : "Có, hoàn thành"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
