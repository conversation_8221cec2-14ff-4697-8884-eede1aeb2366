import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { format, startOfMonth } from "date-fns";
import { ArrowBigRight, CalendarIcon } from "lucide-react";
import { useState } from "react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { HistoryDetail } from "./history-detail";
import { HistoryStaff } from "./history-staff";
import { HistoryGeneral } from "./history-general";

type ComponentProps = {
  refetch: () => void;
  product: IProduct;
};

export function HistoryProduct({ product }: ComponentProps) {
  const [open, setOpen] = useState(false);

  const [fromDate, setFromDate] = useState<Date | undefined>(
    startOfMonth(new Date())
  );
  const [fromCalendarOpen, setFromCalendarOpen] = useState(false);

  const [toDate, setToDate] = useState<Date | undefined>(new Date());
  const [toCalendarOpen, setToCalendarOpen] = useState(false);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="default" className="m-1">
          Lịch sử
        </Button>
      </DialogTrigger>
      <DialogContent className="min-w-[90%] h-[95%] flex flex-col overflow-auto">
        <DialogHeader>
          <DialogTitle>Lịch sử sản phẩm {product.name}</DialogTitle>
        </DialogHeader>
        <DialogDescription></DialogDescription>

        <div className="flex-1 flex overflow-auto">
          <Tabs defaultValue="general" className="flex-1 flex flex-col">
            <div className="flex justify-between items-center flex-wrap">
              <TabsList>
                <TabsTrigger value="general">Tổng quan</TabsTrigger>
                <TabsTrigger value="staff">Nhân viên</TabsTrigger>
                <TabsTrigger value="detail">Chi tiết</TabsTrigger>
              </TabsList>
              <div className="flex gap-2 items-center">
                <Popover
                  open={fromCalendarOpen}
                  onOpenChange={setFromCalendarOpen}
                >
                  <PopoverTrigger asChild>
                    <Button
                      variant={"outline"}
                      className={cn(
                        "w-[140px] justify-start text-left font-normal",
                        !fromDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {fromDate ? format(fromDate, "dd/MM/yyyy") : "Chọn ngày"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={fromDate}
                      defaultMonth={fromDate}
                      disabled={(date) => date > new Date()}
                      onSelect={(date) => {
                        setFromDate(date);
                        setFromCalendarOpen(false);
                      }}
                      fromYear={2024}
                      toDate={new Date()}
                      captionLayout="dropdown-buttons"
                      components={{
                        Dropdown: (props) => (
                          <select
                            {...props}
                            className="px-2 py-1 mx-2 text-sm border rounded-md bg-white dark:bg-black"
                          />
                        ),
                      }}
                      classNames={{
                        nav_button_previous: "",
                        nav_button_next: "",
                      }}
                    />
                  </PopoverContent>
                </Popover>
                <ArrowBigRight />
                <Popover open={toCalendarOpen} onOpenChange={setToCalendarOpen}>
                  <PopoverTrigger asChild>
                    <Button
                      variant={"outline"}
                      className={cn(
                        "w-[140px] justify-start text-left font-normal",
                        !toDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {toDate ? format(toDate, "dd/MM/yyyy") : "Chọn ngày"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={toDate}
                      defaultMonth={toDate}
                      captionLayout="dropdown-buttons"
                      disabled={(date) => date > new Date()}
                      onSelect={(date) => {
                        setToDate(date);
                        setToCalendarOpen(false);
                      }}
                      fromYear={2024}
                      toDate={new Date()}
                      components={{
                        Dropdown: (props) => (
                          <select
                            {...props}
                            className="px-2 py-1 mx-2 text-sm border rounded-md bg-white dark:bg-black"
                          />
                        ),
                      }}
                      classNames={{
                        nav_button_previous: "",
                        nav_button_next: "",
                      }}
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            <TabsContent value="general" className="flex-1">
              <HistoryGeneral
                enable={true}
                product={product}
                fromDate={fromDate}
                toDate={toDate}
              />
            </TabsContent>

            <TabsContent value="staff">
              <HistoryStaff
                enable={true}
                product={product}
                fromDate={fromDate}
                toDate={toDate}
              />
            </TabsContent>

            <TabsContent value="detail">
              <HistoryDetail
                enable={open}
                product={product}
                fromDate={fromDate}
                toDate={toDate}
              />
            </TabsContent>
          </Tabs>
        </div>

        <DialogFooter></DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
