import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useToast } from "@/components/ui/use-toast";

import { usePost } from "@/hooks/usePost";

import { Loader2 } from "lucide-react";
import { useState } from "react";

type ComponentProps = {
  refetch: () => void;
  product: IProduct;
};

export function DeleteProduct({ refetch, product }: ComponentProps) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  const { toast } = useToast();

  const { mutate } = usePost({
    url: "/product/delete",
    onSuccess: () => {
      setLoading(false);
      refetch();
      setOpen(false);
    },
    onError: () => {
      toast({
        variant: "destructive",
        description: "Có lỗi xảy ra. <PERSON>ui lòng reload lại trang.",
      });
      setLoading(false);
    },
  });

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="destructive" className="m-1">
          Xoá
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Xoá sản phẩm</DialogTitle>
        </DialogHeader>
        <DialogDescription></DialogDescription>

        <div className="text-lg">
          Vẫn còn {product.quantity} {product.unit} trong kho. Bạn có chắc muốn
          xoá sản phẩm <b>{product.name}</b> không? Việc này không thể hoàn tác.
        </div>

        <DialogFooter>
          <Button
            variant="secondary"
            disabled={loading}
            onClick={() => {
              setOpen(false);
            }}
          >
            Không
          </Button>

          <Button
            disabled={loading}
            variant="destructive"
            onClick={() => {
              setLoading(true);
              mutate({ id: product.id });
            }}
          >
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Có, xoá
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
