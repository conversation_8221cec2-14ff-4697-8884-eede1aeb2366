import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

import { usePost } from "@/hooks/usePost";
import { useEffect, useState } from "react";

import { Loader2 } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";

type ComponentProps = {
  productType: IProductType;
  refetch: () => void;
};

export function EditProductType({ productType, refetch }: ComponentProps) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  const { toast } = useToast();
  const [name, setName] = useState(productType.name);

  const { mutate } = usePost({
    url: "inventory/product/type/update",
    onSuccess: () => {
      refetch();
      setLoading(false);
      setOpen(false);
    },
    onError: () => {
      toast({
        variant: "destructive",
        description: "Có lỗi xảy ra. Vui lòng reload lại trang.",
      });
      setLoading(false);
    },
  });

  useEffect(() => {
    if (open) {
      setName(productType.name);
    }
  }, [open, productType]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="secondary" className="my-4 mx-1">
          Sửa loại sản phẩm
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Sửa loại sản phẩm</DialogTitle>
        </DialogHeader>
        <DialogDescription></DialogDescription>

        <div>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                Tên loại
              </Label>
              <Input
                id="name"
                className="col-span-3"
                value={name}
                onChange={(event) => setName(event.target.value)}
              />
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button
            disabled={loading}
            onClick={() => {
              setLoading(true);
              mutate({ id: productType.id, name });
            }}
          >
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Lưu thay đổi
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
