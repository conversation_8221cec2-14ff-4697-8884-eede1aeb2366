import { useGet } from '@/hooks/useGet';
import { ReactNode, createContext, useContext, useEffect, useState } from 'react';

type AuthProviderState = {
  accessToken: string;
  setAccessToken: (theme: string) => void;
  valid: boolean;
  setValid: (arg: boolean) => void;
};

const initialState: AuthProviderState = {
  accessToken: '',
  setAccessToken: () => null,
  valid: false,
  setValid: () => null,
};

const AuthContext = createContext<AuthProviderState>(initialState);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [valid, setValid] = useState(false);
  const [accessToken, setAccessToken] = useState(localStorage.getItem('access-token') || '');

  const { refetch } = useGet({
    url: 'users/me',
    isShop: false,
    onSuccess: () => {
      setValid(true);
    },
  });

  useEffect(() => {
    if (accessToken) refetch();
  }, [accessToken]);

  const value = {
    accessToken,
    setAccessToken: (accessToken: string) => {
      localStorage.setItem('access-token', accessToken);
      setAccessToken(accessToken);
    },
    valid,
    setValid,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export const useAuth = () => {
  const context = useContext(AuthContext);

  if (context === undefined) throw new Error('useTheme must be used within a ThemeProvider');

  return context;
};
