import { useMutation } from '@tanstack/react-query';
import { request } from '../lib/axios';
import { useShop } from './useShop';

type PostData = {
  url: string;
  isShop?: boolean;
  // eslint-disable-next-line
  onSuccess?: (data?: any) => void;
  // eslint-disable-next-line
  onError?: (error?: any) => void;
};

export const usePost = ({ url, isShop = true, onSuccess, onError }: PostData) => {
  const { shop } = useShop();
  let newUrl = url;

  if (isShop) {
    const base = shop?.shopUrl?.replace(/\/+$/, ''); // bỏ / cuối
    const path = url.replace(/^\/+/, ''); // bỏ / đầu
    newUrl = `${base}/api/${path}`;
  }

  return useMutation({
    onSuccess,
    onError,
    mutationFn: (data: object) =>
      request({ url: newUrl, method: 'POST', data, jwt: isShop ? shop?.jwt : undefined }),
  });
};
