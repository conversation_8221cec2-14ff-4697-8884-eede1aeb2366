import { formatTime, formatTimeWithText, secondsToTime } from "@/lib/utils";
import { useEffect, useState } from "react";

export function Timer({
  timeStart,
  withText = false,
  haveTime = 0,
  labelBeforeStart = "",
  labelAfterStart = "",
}: {
  timeStart: number;
  withText?: boolean;
  haveTime: number;
  labelBeforeStart?: string;
  labelAfterStart?: string;
}) {
  const [time, setTime] = useState(Math.floor(Date.now()));

  useEffect(() => {
    const intervalId = setInterval(() => {
      setTime(Math.floor(Date.now()));
    }, 1000);

    return () => clearInterval(intervalId);
  }, []);

  const specTime =
    time < timeStart
      ? secondsToTime(Math.floor((timeStart - time) / 1000), false)
      : secondsToTime(haveTime + Math.floor((time - timeStart) / 1000), false);

  return (
    <>
      {time < timeStart ? labelBeforeStart : labelAfterStart}
      <b>{withText ? formatTimeWithText(specTime) : formatTime(specTime)}</b>
    </>
  );
}
