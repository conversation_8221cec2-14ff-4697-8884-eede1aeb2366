import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON><PERSON>ooter,
  Di<PERSON>Header,
  Di<PERSON><PERSON>itle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";

import { usePost } from "@/hooks/usePost";
import { useState } from "react";
import { useToast } from "@/components/ui/use-toast";

type ComponentProps = {
  task: ITask | null;
  refetch: () => void;
};

export function DeleteTask({ task, refetch }: ComponentProps) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  const { toast } = useToast();

  const { mutate } = usePost({
    url: "/task/delete",
    onSuccess: () => {
      setOpen(false);
      setLoading(false);
      refetch();
    },
    onError: () => {
      toast({
        variant: "destructive",
        description: "Có lỗi xảy ra. Vui lòng reload lại trang.",
      });
      setLoading(false);
    },
  });

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="destructive" className="m-1">
          Xoá
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Xoá dịch vụ</DialogTitle>
        </DialogHeader>
        <DialogDescription></DialogDescription>

        <div className="">
          Bạn có chắc muốn xoá dịch vụ <b>{task?.name}</b> không?
        </div>

        <DialogFooter>
          <Button
            variant="destructive"
            disabled={loading}
            onClick={() => {
              if (task?.id) {
                setLoading(true);
                mutate({ id: task?.id });
              }
            }}
          >
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Xoá dịch vụ
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
