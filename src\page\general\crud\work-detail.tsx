import {
  Dialog,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alog<PERSON><PERSON>le,
  DialogTrigger,
} from "@/components/ui/dialog";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

import { useEffect, useState } from "react";

import { Pencil } from "lucide-react";
import {
  canWorking,
  formatID,
  formatPrice,
  formatTimeClock,
  formatTimeWithText,
  secondsToTime,
  tasksPrice,
  tasksTime,
} from "@/lib/utils";

import { Timer } from "@/components/timer";
import { EditWork } from "./edit-work";
import { ContinueWork } from "./continue-woek";
import { UnlockDialog } from "@/components/unlock-dialog";
import { useData } from "../data-provider";

type ComponentProps = {
  refetch: () => void;
  staff: IStaff;
  work: IWork;
};

export function WorkDetail({ staff, work, refetch }: ComponentProps) {
  const [open, setOpen] = useState(false);
  const [unlock, setUnlock] = useState(false);

  const { isEditable } = useData();

  const selected = work.tasks || [];

  useEffect(() => {
    if (open) {
      setUnlock(false);
    }
  }, [open]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Pencil className="h-3 w-3 font-bold" />
      </DialogTrigger>

      <DialogContent className="flex flex-col min-w-[90%] min-h-[90%]">
        <DialogHeader>
          <DialogTitle>Chi tiết công việc</DialogTitle>
        </DialogHeader>
        <DialogDescription></DialogDescription>

        <div className="grow h-full flex flex-col overflow-auto">
          <div className="grid grid-cols-1 md:grid-cols-3 mb-2">
            <div>
              <h3 className="lg:text-xl font-semibold">
                {`ID Công việc: ${formatID(work.id)}`}
              </h3>

              <h3 className="lg:text-xl font-semibold">
                {`Họ và tên: ${staff.name}`}
              </h3>

              <h3 className="lg:text-xl font-semibold">
                {`Khách đòi: ${work.customerRequest ? "✔️" : "❌"}`}
              </h3>
            </div>

            <div>
              <h3 className="lg:text-xl font-semibold">
                {`Số dịch vụ: ${selected.length}`}
              </h3>

              <h3 className="lg:text-xl font-semibold">
                {`Tổng số tiền: ${formatPrice(tasksPrice(selected))}`}
              </h3>

              <h3 className="lg:text-xl font-semibold">
                {`Thời gian dự kiến: ${tasksTime(selected) / 60} Phút`}
              </h3>
            </div>

            <div>
              {(unlock || !isEditable) && (
                <h3 className="lg:text-xl font-semibold">
                  {work.onWorking
                    ? work.timeStart && (
                        <Timer
                          withText={true}
                          haveTime={work.time || 0}
                          timeStart={new Date(work.timeStart).getTime()}
                          labelAfterStart="Thời gian đã làm: "
                          labelBeforeStart="Thời gian chuẩn bị: "
                        />
                      )
                    : work.time && (
                        <>
                          <h3 className="lg:text-xl font-semibold">
                            {`Thời gian đã làm: ${formatTimeWithText(
                              secondsToTime(work.time, false)
                            )}`}
                          </h3>
                          {work.timeStart && (
                            <h3 className="lg:text-xl font-semibold">
                              {`Thời gian bắt đầu: ${formatTimeClock(
                                new Date(work.timeStart)
                              )}`}
                            </h3>
                          )}

                          {work.timeEnd && (
                            <h3 className="lg:text-xl font-semibold">
                              {`Thời gian kết thúc: ${formatTimeClock(
                                new Date(work.timeEnd)
                              )}`}
                            </h3>
                          )}
                        </>
                      )}
                </h3>
              )}
            </div>
          </div>

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[100px]">STT</TableHead>
                <TableHead>Dịch vụ</TableHead>
                <TableHead className="text-center">Giá tiền</TableHead>
                <TableHead className="text-center">Thời gian dự kiến</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {selected.map((el, index) => (
                <TableRow key={index}>
                  <TableCell>{index + 1}</TableCell>
                  <TableCell>{el.name}</TableCell>
                  <TableCell className="text-center">
                    {formatPrice(el.price)}
                  </TableCell>
                  <TableCell className="text-center">
                    {`${el.time / 60} Phút`}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        <DialogFooter>
          {isEditable && (
            <>
              <UnlockDialog unlock={unlock} setUnlock={setUnlock} />
              {canWorking(staff.today) && !work.onWorking && (
                <ContinueWork work={work} staff={staff} refetch={refetch} />
              )}
              <EditWork
                unlock={unlock}
                setUnlock={setUnlock}
                work={work}
                staff={staff}
                refetch={refetch}
              />
            </>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
