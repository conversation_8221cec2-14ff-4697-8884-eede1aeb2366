import { differenceInDays, format, parseISO } from "date-fns";
import { useEffect, useState } from "react";
import { TransactionType } from "./create-transaction";
import { useGet } from "@/hooks/useGet";
import {
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  ResponsiveContainer,
  Tooltip,
  Legend,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
} from "recharts";
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs";

type ITransactionGeneral = {
  transactionType: TransactionType;
  totalQuantity: number;
};

type ITransactionGroup = {
  groupKey: string;
  transactionType: TransactionType;
  totalQuantity: number;
};

type ChartDataType = {
  date: string;
  IMPORT: number;
  EXPORT: number;
};

type HistoryStaffProps = {
  enable: boolean;
  product: IProduct;
  fromDate: Date | undefined;
  toDate: Date | undefined;
};

const GROUP_BY = ["day", "week", "month", "year"];

function getGroupByLabel(groupBy: string) {
  switch (groupBy) {
    case "day":
      return "ngày";
    case "week":
      return "tuần";
    case "month":
      return "tháng";
    case "year":
      return "năm";
    default:
      return groupBy;
  }
}

function formatGroupKey(groupKey: string, groupBy: string) {
  if (groupBy === "day") {
    return format(parseISO(groupKey), "dd/MM/yyyy");
  }

  if (groupBy === "week") {
    const year = Math.floor(Number(groupKey) / 100);
    const week = Number(groupKey) % 100;
    return `Tuần ${week} - ${year}`;
  }

  if (groupBy === "month") {
    const year = Math.floor(Number(groupKey) / 100);
    const month = Number(groupKey) % 100;
    return `${month}/${year}`;
  }

  return groupKey.toString();
}

const COLORS = {
  IMPORT: "#4ade80",
  EXPORT: "#f87171",
};

const labelMap: Record<string, string> = {
  IMPORT: "Nhập hàng",
  EXPORT: "Xuất hàng",
};

export function HistoryGeneral({
  enable,
  product,
  fromDate,
  toDate,
}: HistoryStaffProps) {
  const [groupBy, setGroupBy] = useState(GROUP_BY[1]);

  const { data: rawData } = useGet({
    enabled: enable,
    url: "/inventory/transaction/general",
    params: {
      productId: product.id,
      fromDateOffset: fromDate ? differenceInDays(new Date(), fromDate) : 0,
      toDateOffset: toDate ? differenceInDays(new Date(), toDate) : undefined,
    },
  });

  const { data: groupData } = useGet({
    enabled: enable,
    url: "/inventory/transaction/group",
    params: {
      productId: product.id,
      fromDateOffset: fromDate ? differenceInDays(new Date(), fromDate) : 0,
      toDateOffset: toDate ? differenceInDays(new Date(), toDate) : undefined,
      groupBy,
    },
  });

  const [data, setData] = useState([
    { key: "IMPORT", name: "Nhập hàng", value: 0 },
    { key: "EXPORT", name: "Xuất hàng", value: 2 },
  ]);

  const [chartData, setChartData] = useState([]);

  useEffect(() => {
    if (rawData && rawData.length >= 0) {
      const _import = rawData.find(
        (el: ITransactionGeneral) =>
          el.transactionType == TransactionType.IMPORT
      )?.totalQuantity;

      const _export = rawData.find(
        (el: ITransactionGeneral) =>
          el.transactionType == TransactionType.EXPORT
      )?.totalQuantity;

      setData([
        { key: "IMPORT", name: "Nhập hàng", value: Number(_import) || 0 },
        { key: "EXPORT", name: "Xuất hàng", value: Number(_export) || 0 },
      ]);
    }
  }, [rawData]);

  useEffect(() => {
    if (groupData) {
      const chartData = groupData.reduce(
        (acc: ChartDataType[], item: ITransactionGroup) => {
          const label = formatGroupKey(item.groupKey, groupBy);
          let _index = acc.findIndex((el) => el.date == label);

          if (_index == -1) {
            acc.push({ date: label, IMPORT: 0, EXPORT: 0 });
            _index = acc.findIndex((el) => el.date == label);
          }

          if (item.transactionType == TransactionType.IMPORT) {
            acc[_index]["IMPORT"] = Number(item.totalQuantity);
          } else {
            acc[_index]["EXPORT"] = Number(item.totalQuantity);
          }

          return acc;
        },
        []
      );

      setChartData(chartData);
    }
  }, [groupData]);

  return (
    <div className="w-full h-full flex flex-col gap-8">
      {/* Pie Chart */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="w-full h-72 flex flex-col">
          <h3 className="text-xl font-semibold mb-2 text-center">
            Tỷ lệ nhập / xuất
          </h3>
          <ResponsiveContainer>
            <PieChart>
              <Pie
                data={data}
                dataKey="value"
                nameKey="name"
                outerRadius={70}
                label={({ name, value }) =>
                  `${labelMap[name] || name}: ${value}`
                }
              >
                {data.map((entry) => (
                  <Cell
                    key={entry.key}
                    fill={COLORS[entry.key as keyof typeof COLORS]}
                  />
                ))}
              </Pie>
              <Tooltip
                formatter={(value, name) => [
                  value,
                  labelMap[name as string] || name,
                ]}
              />
              <Legend formatter={(value) => labelMap[value] || value} />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Bar Chart */}
      <div className="w-full h-[25rem] flex flex-col items-center">
        <h3 className="text-xl font-semibold mb-2 text-center">
          Số lượng nhập / xuất theo {getGroupByLabel(groupBy)}
        </h3>

        <Tabs
          value={groupBy}
          onValueChange={(v) => setGroupBy(v)}
          className="mb-2"
        >
          <TabsList className="h-9 gap-1">
            {GROUP_BY.map((el) => (
              <TabsTrigger
                key={el}
                value={el}
                className="text-sm h-8 px-3 py-1"
              >
                {getGroupByLabel(el)}
              </TabsTrigger>
            ))}
          </TabsList>
        </Tabs>

        <ResponsiveContainer>
          <BarChart data={chartData} barGap={0} barCategoryGap="30%">
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="date" />
            <YAxis />
            <Tooltip
              formatter={(value, name) => [
                value,
                labelMap[name as string] || name,
              ]}
            />
            <Legend formatter={(value) => labelMap[value] || value} />
            <Bar
              dataKey="IMPORT"
              name="Nhập hàng"
              fill={COLORS.IMPORT}
              barSize={50}
            />
            <Bar
              dataKey="EXPORT"
              name="Xuất hàng"
              fill={COLORS.EXPORT}
              barSize={50}
            />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
}
