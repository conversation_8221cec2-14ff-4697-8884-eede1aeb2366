import {
  Sheet,
  SheetContent,
  SheetDescription,
  Sheet<PERSON>itle,
  SheetTrigger,
} from '@/components/ui/sheet';
import { Menu } from 'lucide-react';
import { ModeToggle } from './mode-toggle';
import { Button } from './ui/button';
import { today } from '@/lib/utils';
import { menu, Sidebar } from './side-bar';
import { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { useShop } from '@/hooks/useShop';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';

export function NavBar() {
  const [open, setOpen] = useState(false);
  const { shops, shop, setShop } = useShop();

  const location = useLocation();

  useEffect(() => {
    const menuItem = menu.find((el) => el.path == location.pathname);
    document.title = menuItem?.label
      ? import.meta.env.VITE_TITLE + ' - ' + menuItem.label
      : import.meta.env.VITE_TITLE;
  }, [location]);

  // function fullScreen() {
  //   if (document.fullscreenElement) {
  //     document
  //       .exitFullscreen()
  //       // .then(() => console.log("Document Exited from Full screen mode"))
  //       .catch((err) => console.error(err));
  //   } else {
  //     document.documentElement.requestFullscreen();
  //   }
  // }

  return (
    <div className="h-14 rounded-md border border-b-1 bg-background p-1">
      <div className="w-full flex justify-between items-center">
        <div className="flex items-center">
          <Sheet open={open} onOpenChange={(value) => setOpen(value)}>
            <SheetTrigger asChild>
              <Button variant="ghost">
                <Menu className="h-[1.2rem] w-[1.2rem]" />
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="overflow-y-scroll">
              <div className="h-full flex flex-col justify-between">
                <div>
                  <SheetTitle>{import.meta.env.VITE_TITLE}</SheetTitle>{' '}
                  <SheetDescription> </SheetDescription>
                  <Sidebar setOpen={setOpen} />
                </div>
              </div>
            </SheetContent>
          </Sheet>

          <Select
            value={shop?.id.toString()}
            onValueChange={(value) =>
              setShop(shops.find((s) => s.id.toString() == value) ?? shops[0])
            }
          >
            <SelectTrigger className="w-[300px] text-lg md:text-xl font-bold">
              <SelectValue placeholder="Chọn cửa hàng" />
            </SelectTrigger>
            <SelectContent>
              {shops.map((s) => (
                <SelectItem key={s.id} value={s.id.toString()}>
                  <div className="flex items-center">
                    {s.logoUrl && (
                      <img src={s.logoUrl} alt={s.name} className="w-6 h-6 mr-2 rounded" />
                    )}
                    <div className="truncate">{s.name}</div>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center justify-center">
          <div className="mr-2">{today()}</div>
          <ModeToggle />
          {/* <Button variant="outline" size="icon" onClick={() => fullScreen()}>
            <Maximize className="h-[1.2rem] w-[1.2rem]" />
          </Button> */}
        </div>
      </div>
    </div>
  );
}
