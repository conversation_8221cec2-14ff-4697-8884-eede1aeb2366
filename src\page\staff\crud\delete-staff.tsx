import {
  Dialog,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  Di<PERSON>Header,
  Di<PERSON><PERSON><PERSON>le,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";

import { usePost } from "@/hooks/usePost";
import { Loader2 } from "lucide-react";
import { useState } from "react";
import { useToast } from "@/components/ui/use-toast";

type ComponentProps = {
  staff: IStaff | null;
  refetch: () => void;
};

export function DeleteStaff({ staff, refetch }: ComponentProps) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  const { toast } = useToast();

  const { mutate } = usePost({
    url: "/staff/delete",
    onSuccess: () => {
      setOpen(false);
      setLoading(false);
      refetch();
    },
    onError: () => {
      toast({
        variant: "destructive",
        description: "Có lỗi xảy ra. Vui lòng reload lại trang.",
      });
      setLoading(false);
    },
  });

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="destructive" className="m-1">
          Xoá
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Xoá nhân viên</DialogTitle>
        </DialogHeader>
        <DialogDescription></DialogDescription>

        <div className="">
          Bạn có chắc muốn xoá nhân viên <b>{staff?.name}</b> không?
        </div>

        <DialogFooter>
          <Button
            disabled={loading}
            variant="destructive"
            onClick={() => {
              if (staff?.id) {
                setLoading(true);
                mutate({ id: staff?.id });
              }
            }}
          >
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Xoá nhân viên
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
