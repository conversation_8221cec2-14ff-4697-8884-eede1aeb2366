import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";

import { usePost } from "@/hooks/usePost";
import { useEffect, useState } from "react";
import { Loader2 } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";

type ComponentProps = {
  taskTypes: ITaskType[];
  task: ITask | null;
  refetch: () => void;
};

export function EditTask({ taskTypes, task, refetch }: ComponentProps) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  const [fullname, setFullname] = useState("");
  const [price, setPrice] = useState(0);
  const [time, setTime] = useState(0);
  const [taskTypeId, setTaskTypeId] = useState(0);

  const { toast } = useToast();

  const { mutate } = usePost({
    url: "/task/update",
    onSuccess: () => {
      refetch();
      setLoading(false);
      setOpen(false);
    },
    onError: () => {
      toast({
        variant: "destructive",
        description: "Có lỗi xảy ra. Vui lòng reload lại trang.",
      });
      setLoading(false);
    },
  });

  useEffect(() => {
    if (task) {
      setFullname(task.name);
      setPrice(task.price / 100);
      setTime(task.time / 60);
      setTaskTypeId(task.taskTypeId);
    }
  }, [task]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="secondary" className="m-1">
          Sửa
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Sửa dịch vụ</DialogTitle>
        </DialogHeader>
        <DialogDescription></DialogDescription>

        <div>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">Tên công việc</Label>
              <Input
                className="col-span-3"
                value={fullname}
                onChange={(event) => setFullname(event.target.value)}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">Thời gian (phút)</Label>
              <Input
                className="col-span-3"
                type="number"
                value={Number(time).toString()}
                onChange={(event) => setTime(parseInt(event.target.value))}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">Giá bán</Label>
              <Input
                className="col-span-3"
                type="number"
                value={Number(price).toString()}
                onChange={(event) => setPrice(parseInt(event.target.value))}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">Loại công việc</Label>
              <Select
                value={taskTypeId.toString()}
                onValueChange={(value) => setTaskTypeId(parseInt(value))}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {taskTypes.map((el) => (
                    <SelectItem key={el.id} value={el.id.toString()}>
                      {el.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button
            disabled={loading}
            onClick={() => {
              if (task?.id) {
                setLoading(true);
                mutate({
                  id: task?.id,
                  name: fullname,
                  price: price * 100,
                  time: time * 60,
                  taskTypeId,
                });
              }
            }}
          >
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Sửa dịch vụ
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
