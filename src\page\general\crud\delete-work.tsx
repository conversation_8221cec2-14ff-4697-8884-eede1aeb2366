import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogDescription,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/components/ui/use-toast";

import { usePost } from "@/hooks/usePost";

import { Loader2, X } from "lucide-react";
import { useEffect, useState } from "react";

type ComponentProps = {
  refetch: () => void;
  staff: IStaff;
  work: IWork;
};

export function DeleteWork({ refetch, staff, work }: ComponentProps) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [password, setPassword] = useState("");

  const { toast } = useToast();

  useEffect(() => {
    if (open) {
      setPassword("");
    }
  }, [open]);

  const { mutate } = usePost({
    url: "/work/delete",
    onSuccess: () => {
      setLoading(false);
      refetch();
      setOpen(false);
    },
    onError: () => {
      toast({
        variant: "destructive",
        description: "Có lỗi xảy ra. Vui lòng reload lại trang.",
      });
      setLoading(false);
    },
  });

  const { mutate: signInMutate } = usePost({
    url: `/auth/sign-in`,
    onSuccess: () => {
      mutate({ id: work.id });
    },
    onError: () => {
      toast({
        variant: "destructive",
        description: "Sai mật khẩu.",
      });
      setLoading(false);
    },
  });

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <X className="h-4 w-4 font-bold" />
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Xoá công việc</DialogTitle>
        </DialogHeader>
        <DialogDescription></DialogDescription>

        <div className="">
          Bạn có chắc chắn muốn <b>{` XOÁ `}</b> công việc của
          <b>{` ${staff.name} `}</b>không?
        </div>

        <div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label className="text-left">Mật khẩu</Label>
            <Input
              type="password"
              className="col-span-3"
              value={password}
              onChange={(event) => setPassword(event.target.value)}
            />
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="secondary"
            disabled={loading}
            onClick={() => {
              setOpen(false);
            }}
          >
            Không
          </Button>

          <Button
            disabled={loading}
            variant="destructive"
            onClick={() => {
              setLoading(true);
              signInMutate({ username: "admin", password });
            }}
          >
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Có, xoá
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
