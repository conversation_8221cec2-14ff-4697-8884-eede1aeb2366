import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Des<PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";

import { usePost } from "@/hooks/usePost";
import { useEffect, useState } from "react";

import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";

import { Loader2 } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { useStaffType } from "@/hooks/useStaffType";

type ComponentProps = {
  staff: IStaff | null;
  refetch: () => void;
};

export function EditStaff({ staff, refetch }: ComponentProps) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  const { toast } = useToast();

  const [fullname, setFullname] = useState("");
  const [staffTypeId, setStaffTypeId] = useState<number>();

  const { data: taskTypes } = useStaffType();

  const { mutate } = usePost({
    url: "/staff/update",
    onSuccess: () => {
      setLoading(false);
      refetch();
      setOpen(false);
    },
    onError: () => {
      toast({
        variant: "destructive",
        description: "Có lỗi xảy ra. Vui lòng reload lại trang.",
      });
      setLoading(false);
    },
  });

  useEffect(() => {
    if (staff) {
      setFullname(staff.name);
      setStaffTypeId(staff.staffTypeId);
    }
  }, [staff]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="secondary" className="m-1">
          Sửa
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Sửa nhân viên</DialogTitle>
        </DialogHeader>
        <DialogDescription></DialogDescription>

        <div className="">
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">Họ và tên</Label>
              <Input
                className="col-span-3"
                value={fullname}
                onChange={(event) => {
                  setFullname(event.target.value);
                }}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">Dịch vụ</Label>
              <Select
                value={staffTypeId?.toString()}
                onValueChange={(value) => setStaffTypeId(parseInt(value))}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {taskTypes.map((el) => (
                    <SelectItem key={el.id} value={el.id.toString()}>
                      {el.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button
            disabled={loading}
            variant="destructive"
            onClick={() => {
              if (staff?.id) {
                setLoading(true);
                mutate({ id: staff?.id, name: fullname, staffTypeId });
              }
            }}
          >
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Sửa nhân viên
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
