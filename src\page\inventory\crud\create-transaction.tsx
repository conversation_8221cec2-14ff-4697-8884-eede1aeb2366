import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Content,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Trigger,
} from "@/components/ui/dialog";

import { But<PERSON> } from "@/components/ui/button";

import { usePost } from "@/hooks/usePost";
import { ReactNode, useEffect, useState } from "react";
import { Loader2, Plus, XIcon } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { format } from "date-fns";
import { AddProduct } from "./add-product";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";

type ComponentProps = {
  refetch: () => void;
  productTypes: IProductType[];
  products: IProduct[];
  transactionType?: TransactionType;
  staff?: IStaff;
  renderButton?: ReactNode;
};

export enum TransactionType {
  NONE = "NONE",
  IMPORT = "IMPORT",
  EXPORT = "EXPORT",
}

function capitalize(str: string) {
  if (!str) return "";
  str = str.trim();
  return str[0].toUpperCase() + str.slice(1).toLowerCase();
}

interface ProductItem {
  product: IProduct;
  quantity: number;
  note: string;
}

export function CreateTransaction({
  refetch,
  productTypes,
  products,
  transactionType = TransactionType.IMPORT,
  staff,
  renderButton,
}: ComponentProps) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [tab, setTab] = useState<string>("1");
  const [unitFilter, setUnitFilter] = useState<string>("-1");

  const [globalNote, setGlobalNote] = useState("");
  const [items, setItems] = useState<ProductItem[]>([]);

  const { toast } = useToast();

  const { mutate } = usePost({
    url: "/inventory/transaction/create",
    onSuccess: () => {
      setLoading(false);
      toast({
        variant: "default",
        description: "Tạo phiếu thành công.",
      });
      refetch();
      setOpen(false);
    },
    onError: (error) => {
      toast({
        variant: "destructive",
        description:
          error?.message || "Có lỗi xảy ra. Vui lòng reload lại trang.",
      });
      setLoading(false);
    },
  });

  useEffect(() => {
    if (!open) return;
    if (productTypes.length > 0) {
      setTab(productTypes[0].id.toString());
    }

    setItems([]);
    setGlobalNote("");
    setUnitFilter("-1");
  }, [open]);

  useEffect(() => {
    setUnitFilter("-1");
  }, [tab]);

  const addItems = (product: IProduct) => {
    const duplicateItem = items.find((el) => el.product.id == product.id);
    const itemInProducts = products.find((el) => el.id == product.id);

    if (duplicateItem) {
      duplicateItem.quantity += 1;

      if (itemInProducts && transactionType == TransactionType.EXPORT)
        if (itemInProducts.quantity < duplicateItem.quantity)
          duplicateItem.quantity = itemInProducts.quantity;

      setItems([...items]);
      return;
    }

    const newItems = [...items, { product: product, quantity: 1, note: "" }];
    setItems(newItems);
  };

  const handleChangeQuantity = (item: ProductItem, value: string) => {
    const findItem = items.find((el) => el.product.id == item.product.id);
    if (!findItem) return;
    findItem.quantity = parseInt(value);
    setItems([...items]);
  };

  const removeItem = (item: ProductItem) => {
    const newItems = items.filter((el) => el.product.id != item.product.id);
    setItems(newItems);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {renderButton || (
          <Button className="my-4 mx-1" variant="default" size="sm">
            <Plus className="mr-2 h-4 w-4" /> Nhập hàng
          </Button>
        )}
      </DialogTrigger>

      <DialogContent className="flex flex-col min-w-[90%] min-h-[90%]">
        <DialogHeader>
          <DialogTitle>
            {transactionType == TransactionType.IMPORT
              ? "Phiếu nhập hàng"
              : "Phiếu xuất hàng"}
          </DialogTitle>
        </DialogHeader>

        <DialogDescription></DialogDescription>

        <div className="flex-1 flex flex-col">
          <div className="grid grid-cols-3 gap-4 flex-1">
            <div className="col-span-2 flex flex-col">
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <Tabs value={tab} onValueChange={(value) => setTab(value)}>
                    <div className="flex justify-between items-center">
                      <TabsList>
                        {productTypes.map((el) => (
                          <TabsTrigger key={el.id} value={el.id.toString()}>
                            {el.name}
                          </TabsTrigger>
                        ))}
                      </TabsList>
                    </div>
                  </Tabs>
                </div>
              </div>

              <div className="mt-2 flex justify-between items-center">
                <Tabs
                  value={unitFilter}
                  onValueChange={(value) => setUnitFilter(value)}
                >
                  <div className="flex justify-between items-center">
                    <TabsList>
                      <TabsTrigger value={"-1"} className="text-base px-2 py-1">
                        Toàn bộ
                      </TabsTrigger>

                      {[
                        ...new Set(
                          products
                            .filter((el) => el.productTypeId.toString() == tab)
                            .map((p) => capitalize(p.unit))
                        ),
                      ].map((el) => (
                        <TabsTrigger key={el} value={el} className="text-base">
                          {el}
                        </TabsTrigger>
                      ))}
                    </TabsList>
                  </div>
                </Tabs>

                <AddProduct
                  productTypes={productTypes}
                  refetch={refetch}
                  tab={tab}
                  unitFilter={unitFilter}
                />
              </div>

              <div className="border grow overflow-auto flex-1">
                <div className="flex flex-wrap overflow-auto px-3 py-1.5">
                  {products
                    .filter((el) => el.productTypeId.toString() == tab)
                    .filter(
                      (el) =>
                        capitalize(el.unit) == unitFilter || unitFilter == "-1"
                    )
                    .map((el: IProduct) => (
                      <div
                        key={el.id}
                        onClick={() => addItems(el)}
                        className="hover:cursor-pointer border-2 rounded-lg border-orange-500  bg-card text-card-foreground shadow-sm max-h-[120px] h-[120px] w-[120px] m-2"
                      >
                        <div className="h-full w-full flex flex-col">
                          <div className="grow px-3 py-3 text-center flex flex-col justify-center">
                            <div className="lg:text-lg font-bold text-center overflow-clip">
                              {el.name}
                            </div>
                          </div>
                          <div className="mt-2 w-full py-1 text-center bg-orange-600 text-white">
                            Kho: {el.quantity}
                          </div>
                        </div>
                      </div>
                    ))}
                </div>
              </div>
            </div>

            <div className="mb-8 flex flex-col gap-2">
              <h3 className="lg:text-2xl font-bold uppercase text-center mb-6">
                {transactionType == TransactionType.IMPORT
                  ? "Phiếu nhập hàng"
                  : "Phiếu xuất hàng"}
              </h3>

              <div className="lg:text-xl font-semibold">
                <h3 className="">
                  {format(new Date(), "HH:mm:ss, dd/MM/yyyy")}
                </h3>
                {staff && <h3 className="">{`Nhân viên: ${staff.name}`}</h3>}
                <div className="mr-2">
                  <Label className="lg:text-xl">Ghi chú:</Label>
                  <Textarea
                    className="ml-0.5 lg:text-xl"
                    value={globalNote}
                    onChange={(event) => setGlobalNote(event.target.value)}
                  />
                </div>
              </div>

              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[50px]">STT</TableHead>
                    <TableHead>Tên sản phẩm</TableHead>
                    <TableHead className="w-[120px]">Số lượng</TableHead>
                    <TableHead className="w-[100px]">Đơn vị</TableHead>
                    <TableCell className="w-[50px]"></TableCell>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {items.map((item, index) => (
                    <TableRow key={index}>
                      <TableCell className="font-medium">{index + 1}</TableCell>
                      <TableCell>{item.product.name || ""}</TableCell>
                      <TableCell>
                        <Input
                          className="w-full"
                          value={item.quantity}
                          type="number"
                          onChange={(event) =>
                            handleChangeQuantity(item, event.target.value)
                          }
                        />
                      </TableCell>
                      <TableCell>{capitalize(item.product.unit)}</TableCell>
                      <TableCell className="font-medium p-0 px-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => removeItem(item)}
                          className="hover:bg-red-500/10 hover:scale-150 transition-transform duration-300"
                        >
                          <XIcon className="w-4 h-4" color="red" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button
            disabled={loading}
            variant="destructive"
            onClick={() => {
              setLoading(true);
              mutate({
                transactionType,
                note: globalNote,
                staffId: staff?.id,
                details: items
                  .filter((el) => el.product != null && el.quantity > 0)
                  .map((el) => ({
                    productId: el.product?.id,
                    quantity: el.quantity,
                    note: el.note,
                  })),
              });
            }}
          >
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {transactionType == TransactionType.IMPORT
              ? "Nhập hàng"
              : "Xuất hàng"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
