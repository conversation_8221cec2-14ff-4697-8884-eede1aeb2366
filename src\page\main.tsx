import { Loading } from '@/components/loading';
import { useGet } from '@/hooks/useGet';
import { useShop } from '@/hooks/useShop';
import { useAuth } from '@/providers/auth-provider';
import { useEffect } from 'react';

import { Navigate, Outlet, useLocation } from 'react-router-dom';

export function Main() {
  const { valid } = useAuth();
  const { pathname } = useLocation();
  const { setShops, setShop, shop } = useShop();

  const { data } = useGet({
    url: '/shops',
    enabled: valid,
    isShop: false,
  });

  useEffect(() => {
    if (data?.data?.length > 0) {
      setShop(data?.data[0]);
      setShops(data?.data);
    }
  }, [data]);

  if (!valid) return <Navigate to={`/sign-in?redirectTo=${pathname}`} />;
  if (!shop) return <Loading />;
  return <Outlet />;
}
