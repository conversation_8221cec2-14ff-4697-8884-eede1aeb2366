import { Fragment, useEffect, useState } from 'react';
import { useGet } from '@/hooks/useGet';
import { BadgePlus, MoveRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
// import { usePost } from '@/hooks/usePost';
// import { useToast } from '@/components/ui/use-toast';
import { Input } from '@/components/ui/input';
import { defaultSetting } from '@/lib/constants';
import { useStaffType } from '@/hooks/useStaffType';

import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

const WEEKS = [
  { label: 'Thứ Hai', value: 1 },
  { label: 'Thứ Ba', value: 2 },
  { label: 'Thứ Tư', value: 3 },
  { label: 'Thứ Năm', value: 4 },
  { label: '<PERSON><PERSON><PERSON>', value: 5 },
  { label: '<PERSON><PERSON><PERSON>', value: 6 },
  { label: '<PERSON><PERSON> nhật', value: 7 },
];

function getDaysInRange(start: number, end: number) {
  let daysInRange = [];

  if (start > end) {
    daysInRange = WEEKS.filter((day) => day.value >= start && day.value <= 7).map(
      (day) => day.label,
    );

    daysInRange = daysInRange.concat(
      WEEKS.filter((day) => day.value >= 1 && day.value <= end).map((day) => day.label),
    );
  } else {
    daysInRange = WEEKS.filter((day) => day.value >= start && day.value <= end).map(
      (day) => day.label,
    );
  }

  return daysInRange.join(', ');
}

export function Setting() {
  const [settingData, setSettingData] = useState<ISettingData>(defaultSetting);
  // const [loading, setLoading] = useState(false);
  const [staffTypes, setStaffTypes] = useState<IStaffType[]>([]);
  const [open, setOpen] = useState(false);

  const { data } = useGet({ url: '/user/setting' });
  const { data: stData } = useStaffType();

  // const { toast } = useToast();

  // const { mutate } = usePost({
  //   url: `/user/change-setting`,
  //   onSuccess: () => {
  //     toast({
  //       variant: 'default',
  //       description: 'Thay đổi cài đặt thành công.',
  //     });
  //     setLoading(false);
  //   },
  //   onError: () => {
  //     toast({
  //       variant: 'destructive',
  //       description: 'Có lỗi xảy ra. Vui lòng reload lại trang.',
  //     });
  //     setLoading(false);
  //   },
  // });

  useEffect(() => {
    if (data) {
      setSettingData(data);
    }
  }, [data]);

  useEffect(() => {
    if (stData && stData.length > 0) {
      setStaffTypes(stData);
    }
  }, [stData]);

  return (
    <div className="z-20 w-full-h-full grow flex items-center justify-center">
      <div className="w-full rounded-lg shadow border border-gray-700 md:mt-0 sm:max-w-md xl:p-0 bg-gray-800">
        <div className="p-6 space-y-4 md:space-y-6 sm:p-8">
          <h1 className="text-xl font-bold leading-tight text-gray-900 md:text-2xl text-white">
            Cài đặt
          </h1>

          <div className="space-y-4 md:space-y-6">
            <div>
              <label className="block mb-2 text-sm font-medium text-white">Số tiền tối thiểu</label>
              <Input
                readOnly
                type="number"
                value={settingData.minPrice / 100}
                onChange={(ev) =>
                  setSettingData((settingData) => ({
                    ...settingData,
                    minPrice: parseInt(ev.target.value) * 100,
                  }))
                }
                className="rounded-lg block w-full p-2.5 bg-gray-700 border-gray-600 placeholder-gray-400 text-white focus:ring-blue-500 focus:border-blue-500"
              />
              <div className="text-white text-xs m-0.5">
                Số tiền tối thiểu để tính turn và turn cuối.
              </div>
            </div>

            <div>
              <label className="block mb-2 text-sm font-medium text-white">
                Số khách đòi chuyển đổi
              </label>
              <Input
                readOnly
                type="number"
                value={settingData.convertCusReq}
                onChange={(ev) =>
                  setSettingData((settingData) => ({
                    ...settingData,
                    convertCusReq: parseInt(ev.target.value),
                  }))
                }
                className="rounded-lg block w-full p-2.5 bg-gray-700 border-gray-600 placeholder-gray-400 text-white focus:ring-blue-500 focus:border-blue-500"
              />
              <div className="text-white text-xs m-0.5 mt-1">
                Số khách đòi chuyển thành 1 turn. Ví dụ 2 khách đòi sẽ tăng 1 turn.
              </div>
              <div className="text-white text-xs m-0.5">
                0 nghĩa là sẽ không chuyển đổi số khách đòi thành turn.
              </div>
            </div>

            <div>
              <label className="block mb-2 text-sm font-medium text-white">Ngày áp dụng</label>

              <div className="flex justify-between items-center">
                <Select
                  value={settingData.applyStart.toString()}
                  // onValueChange={(value) =>
                  //   setSettingData((settingData) => ({
                  //     ...settingData,
                  //     applyStart: parseInt(value),
                  //   }))
                  // }
                >
                  <SelectTrigger className="focus:ring-0 w-[120px] rounded-lg bg-gray-700 border-gray-600 text-white">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-700 border-gray-600 text-white">
                    <SelectGroup>
                      {WEEKS.map((el, index) => (
                        <SelectItem key={index} value={el.value.toString()}>
                          {el.label}
                        </SelectItem>
                      ))}
                    </SelectGroup>
                  </SelectContent>
                </Select>

                <MoveRight className="text-white w-5 h- mx-2" />

                <Select
                  value={settingData.applyEnd.toString()}
                  // onValueChange={(value) =>
                  //   setSettingData((settingData) => ({
                  //     ...settingData,
                  //     applyEnd: parseInt(value),
                  //   }))
                  // }
                >
                  <SelectTrigger className="focus:ring-0 w-[120px] rounded-lg bg-gray-700 border-gray-600 text-white">
                    <SelectValue placeholder="Select a fruit" />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-700 border-gray-600 text-white">
                    <SelectGroup>
                      {WEEKS.map((el, index) => (
                        <SelectItem key={index} value={el.value.toString()}>
                          {el.label}
                        </SelectItem>
                      ))}
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>

              <div className="text-white text-xs m-0.5 mt-1">
                {`Áp dụng cho các thứ: ${getDaysInRange(
                  settingData.applyStart,
                  settingData.applyEnd,
                )}.`}
              </div>
            </div>

            <div>
              <label className="block mb-2 text-sm font-medium text-white">
                Áp dụng chuyển đổi số khách đòi cho các danh mục
              </label>
              <div className="rounded-lg block w-full p-2.5 bg-gray-700 border-gray-600 placeholder-gray-400 text-white focus:ring-blue-500 focus:border-blue-500">
                <div className="flex justify-between">
                  <div>
                    {settingData.applyRuleStaffType.map((el, index) => {
                      const _temp = staffTypes.filter((staffType) => staffType.id == el);

                      if (_temp.length <= 0) return <Fragment key={index}></Fragment>;

                      const _staffType = _temp[0];
                      return (
                        <Button
                          key={index}
                          variant="destructive"
                          className="text-base mr-4"
                          // onClick={() => {
                          //   setSettingData((settingData) => ({
                          //     ...settingData,
                          //     applyRuleStaffType: settingData.applyRuleStaffType.filter(
                          //       (_, _index) => _index != index,
                          //     ),
                          //   }));
                          // }}
                        >
                          {_staffType.name}
                        </Button>
                      );
                    })}
                  </div>

                  <Popover open={open}>
                    <PopoverTrigger asChild>
                      <Button variant="ghost">
                        <BadgePlus className="w-5 h-5" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="border-4 border-sky-500 w-60 flex flex-col">
                      {staffTypes
                        .filter(
                          (staffType) =>
                            settingData.applyRuleStaffType.filter((el) => el == staffType.id)
                              .length <= 0,
                        )
                        .map((staffType, index) => (
                          <Button
                            key={index}
                            className="my-0.5 text-base"
                            variant="destructive"
                            onClick={() => {
                              setSettingData((settingData) => ({
                                ...settingData,
                                applyRuleStaffType: [
                                  ...settingData.applyRuleStaffType,
                                  staffType.id,
                                ],
                              }));
                              setOpen(false);
                            }}
                          >
                            {staffType.name}
                          </Button>
                        ))}
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
              <div className="text-white text-xs m-0.5 mt-1">
                Nhấn vào danh mục đang có để xoá danh mục
              </div>
              <div className="text-white text-xs m-0.5">
                Chỉ những danh mục được liệt kê ở trên mới quy đổi số khách đòi thành turn
              </div>
            </div>
            {/* 
            <Button
              disabled={loading}
              onClick={() => {
                setLoading(true);
                mutate(settingData);
              }}
              className="w-full text-white bg-blue-700 hover:bg-primary-700 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
            >
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Thay đổi
            </Button> */}
          </div>
        </div>
      </div>
    </div>
  );
}
