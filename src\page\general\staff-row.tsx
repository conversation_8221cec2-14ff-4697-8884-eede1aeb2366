import { TableCell, TableRow } from "@/components/ui/table";

import { useDrag, useDrop } from "react-dnd";
import { Button } from "@/components/ui/button";
import { Grip } from "lucide-react";
import { WorkCtrl } from "./work-ctrl";
import { WorkData } from "./work-data";
import { calTurn, labelCurrentStatus, totalCustomers } from "@/lib/utils";
import { useEffect, useState } from "react";
import { useData } from "./data-provider";

type ComponentProps = {
  rawStaff: IStaff;
  staffIndex: number;
  reorderRow: (draggedRowId: number, targetRowId: number) => void;
  refetch: () => void;
};

export function StaffRow({
  rawStaff,
  staffIndex,
  reorderRow,
  refetch,
}: ComponentProps) {
  const [staff, setStaff] = useState<IStaff>(rawStaff);
  const { settingData, orders, isEditable } = useData();

  useEffect(() => {
    if (rawStaff) {
      setStaff(rawStaff);
    }
  }, [rawStaff]);

  const [, dropRef] = useDrop({
    accept: "row",
    drop: (draggedStaff: IStaff) => {
      reorderRow(draggedStaff.id, staff.id);
    },
  });

  const [{ isDragging }, dragRef, previewRef] = useDrag({
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
    item: () => staff,
    type: "row",
  });

  if (!staff) return <></>;

  return (
    <TableRow
      ref={previewRef}
      className={`md:text-md lg:text-lg font-bold ${
        isDragging ? "opacity-50" : "opacity-100"
      } ${
        staff.today?.isLeave || staff.today?.isBusy
          ? "bg-yellow-900 text-white hover:bg-yellow-900"
          : ""
      }`}
    >
      {isEditable && (
        <TableCell ref={dropRef} className="py-0 px-2">
          <Button
            size="icon"
            variant="ghost"
            ref={dragRef}
            className="cursor-move"
          >
            <Grip className="w-4 h-4" />
          </Button>
        </TableCell>
      )}

      <TableCell className="text-center py-0 px-2">{staffIndex}</TableCell>
      <TableCell className="text-xl py-0 px-2">{staff.name}</TableCell>

      {isEditable && (
        <TableCell className="text-center py-0 px-2 font-black">
          {orders.indexOf(staff.id) >= 0 ? orders.indexOf(staff.id) + 1 : ""}
        </TableCell>
      )}

      {isEditable && (
        <TableCell className="text-center py-0 px-2">
          <WorkCtrl staff={staff} refetch={refetch} />
        </TableCell>
      )}

      <TableCell className="text-center py-0 px-2">
        {totalCustomers(staff, settingData)}
      </TableCell>
      <TableCell className="text-center py-0 px-2">
        {calTurn(staff, settingData)}
      </TableCell>
      <TableCell className="text-center py-0 px-2">
        {staff.today?.customersReq || 0}
      </TableCell>
      <TableCell className="text-center py-0 px-2">
        {staff.today?.specTurn || 0}
      </TableCell>
      <TableCell className="text-center uppercase text-sm text-nowrap py-0 px-2">
        {staff.today ? (
          <StatucLabel info={staff.today} />
        ) : isEditable ? (
          "Chờ khách"
        ) : (
          "Hoàn thành"
        )}
      </TableCell>

      <TableCell className="py-0 px-2">
        <WorkData staff={staff} refetch={refetch} />
      </TableCell>
    </TableRow>
  );
}

function StatucLabel({ info }: { info: IInfo }) {
  const [time, setTime] = useState(Math.floor(Date.now()));
  const { isEditable } = useData();

  useEffect(() => {
    const intervalId = setInterval(() => {
      setTime(Math.floor(Date.now()));
    }, 1000);

    return () => clearInterval(intervalId);
  }, []);

  return labelCurrentStatus(info, time, isEditable);
}
