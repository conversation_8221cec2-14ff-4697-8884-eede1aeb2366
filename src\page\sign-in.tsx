import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { usePost } from '@/hooks/usePost';
import { useEffect, useState } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { Loader2 } from 'lucide-react';
import { useAuth } from '@/providers/auth-provider';
import { Navigate, useSearchParams } from 'react-router-dom';

export function SignIn() {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [searchParams] = useSearchParams();

  const { setAccessToken, valid } = useAuth();
  const { toast } = useToast();

  const { mutate } = usePost({
    url: `/auth/login`,
    isShop: false,
    onSuccess: (data) => {
      const accessToken = data?.data?.accessToken || '';
      if (accessToken !== '') setAccessToken(accessToken);
      setLoading(false);
    },
    onError: () => {
      toast({
        variant: 'destructive',
        description: 'Sai tài khoản hoặc mật khẩu',
      });
      setLoading(false);
    },
  });

  useEffect(() => {
    document.title = import.meta.env.VITE_TITLE + ' - ' + 'Đăng nhập';
  }, []);

  if (valid) return <Navigate to={`${searchParams.get('redirectTo') || '/'}`} />;

  return (
    <div className="z-20 w-full-h-full grow flex items-center justify-center">
      <div className="w-full rounded-lg shadow border border-gray-700 md:mt-0 sm:max-w-md xl:p-0 bg-gray-800">
        <div className="p-6 space-y-4 md:space-y-6 sm:p-8">
          <h1 className="text-xl font-bold leading-tight text-gray-900 md:text-2xl text-white">
            Đăng nhập vào tài khoản
          </h1>

          <form className="space-y-4 md:space-y-6">
            <div>
              <label className="block mb-2 text-sm font-medium text-white">Tài khoản</label>
              <Input
                autoComplete="off"
                type="email"
                name="email"
                value={username}
                onChange={(ev) => setUsername(ev.target.value)}
                className="rounded-lg block w-full p-2.5 bg-gray-700 border-gray-600 placeholder-gray-400 text-white focus:ring-blue-500 focus:border-blue-500"
                placeholder="example"
              />
            </div>
            <div>
              <label className="block mb-2 text-sm font-medium text-white">Mật khẩu</label>
              <input
                autoComplete="on"
                type="password"
                name="password"
                placeholder="••••••••"
                value={password}
                onChange={(ev) => setPassword(ev.target.value)}
                className="rounded-lg block w-full p-2.5 bg-gray-700 border-gray-600 placeholder-gray-400 text-white focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <Button
              disabled={loading}
              onClick={() => {
                setLoading(true);
                mutate({ username, password });
              }}
              className="w-full text-white bg-blue-700 hover:bg-primary-700 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
            >
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Đăng nhập
            </Button>
          </form>
        </div>
      </div>
    </div>
  );
}
