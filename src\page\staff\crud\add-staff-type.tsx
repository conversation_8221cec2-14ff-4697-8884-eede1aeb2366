import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

import { usePost } from "@/hooks/usePost";
import { useEffect, useState } from "react";

import { Loader2, Plus } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";

type ComponentProps = {
  refetch: () => void;
};

export function AddStaffType({ refetch }: ComponentProps) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  const { toast } = useToast();

  const [fullname, setFullname] = useState("");

  const { mutate } = usePost({
    url: "/staff/type/create",
    onSuccess: () => {
      refetch();
      setLoading(false);
      setOpen(false);
    },
    onError: () => {
      toast({
        variant: "destructive",
        description: "Có lỗi xảy ra. Vui lòng reload lại trang.",
      });
      setLoading(false);
    },
  });

  useEffect(() => {
    if (open) {
      setFullname("");
    }
  }, [open]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="default" size="icon">
          <Plus className="h-3 w-3" />
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Thêm danh mục</DialogTitle>
        </DialogHeader>
        <DialogDescription></DialogDescription>

        <div>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                Tên danh mục
              </Label>
              <Input
                id="name"
                className="col-span-3"
                value={fullname}
                onChange={(event) => setFullname(event.target.value)}
              />
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button
            disabled={loading}
            onClick={() => {
              setLoading(true);
              mutate({ name: fullname });
            }}
          >
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Thêm danh mục
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
