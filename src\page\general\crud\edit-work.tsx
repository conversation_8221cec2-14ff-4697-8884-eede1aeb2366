import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { usePost } from "@/hooks/usePost";
import { useEffect, useState } from "react";

import { Delete, Loader2 } from "lucide-react";
import {
  formatID,
  formatPrice,
  formatTimeClock,
  formatTimeWithText,
  secondsToTime,
  tasksPrice,
  tasksTime,
} from "@/lib/utils";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/components/ui/use-toast";
import { Timer } from "@/components/timer";
import { Input } from "@/components/ui/input";
import { UnlockDialog } from "@/components/unlock-dialog";
import { useData } from "../data-provider";

type ComponentProps = {
  refetch: () => void;
  staff: IStaff;
  work: IWork;
  unlock: boolean;
  setUnlock: (arg: boolean) => void;
};

function isHidden(staff: IStaff) {
  return staff.today?.isBusy || staff.today?.onWorking || staff.today?.isLeave;
}

export function EditWork({
  unlock,
  setUnlock,
  refetch,
  staff,
  work,
}: ComponentProps) {
  const [tab, setTab] = useState<string>();
  const [open, setOpen] = useState(false);

  const [selected, setSelected] = useState<ITask[]>([]);
  const [customerRequest, setCustomerRequest] = useState(false);
  const [loading, setLoading] = useState(false);
  const [newStaff, setNewStaff] = useState<IStaff>();
  const [minutes, setMinutes] = useState(0);

  const { toast } = useToast();

  const { mutate } = usePost({
    url: "/work/update",
    onSuccess: () => {
      setLoading(false);
      refetch();
      setOpen(false);
    },
    onError: () => {
      toast({
        variant: "destructive",
        description: "Có lỗi xảy ra. Vui lòng reload lại trang.",
      });
      setLoading(false);
    },
  });

  const { staffs, tasks, taskTypes } = useData();

  useEffect(() => {
    setSelected(work.tasks || []);
    setCustomerRequest(work.customerRequest);
    setNewStaff(staff);
  }, [open]);

  useEffect(() => {
    if (taskTypes?.length > 0 && !tab) {
      setTab(taskTypes[0].id.toString());
    }
  }, [taskTypes]);

  if (!staffs || !newStaff) return <></>;

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>Sửa dịch vụ</Button>
        {/* <Pencil className="h-3 w-3 font-bold" /> */}
      </DialogTrigger>

      <DialogContent className="flex flex-col justify-between min-w-[90%] min-h-[90%]">
        <DialogHeader>
          <DialogTitle>Sửa công việc</DialogTitle>
        </DialogHeader>
        <DialogDescription></DialogDescription>

        <div className="grow h-full flex flex-col overflow-auto">
          <div>
            <div className="grid grid-cols-1 md:grid-cols-3 mb-2">
              <div>
                <h3 className="lg:text-xl font-semibold">
                  {`ID Công việc: ${formatID(work.id)}`}
                </h3>

                <div className="items-baseline flex">
                  <h3 className="lg:text-xl font-semibold mr-2">{`Họ và tên: `}</h3>

                  <Select
                    value={newStaff.id.toString()}
                    onValueChange={(value) =>
                      setNewStaff(staffs.find((el) => el.id == parseInt(value)))
                    }
                  >
                    <SelectTrigger className="text-lg  lg:text-xl  h-6 w-[120px]">
                      <SelectValue />
                    </SelectTrigger>

                    <SelectContent>
                      {staffs
                        .sort((a, b) => a.staffTypeId - b.staffTypeId)
                        // .filter((el) => el.staffTypeId == staff.staffTypeId)
                        .filter(
                          (el) =>
                            !work.onWorking ||
                            (work.onWorking &&
                              (el.id == staff.id || !isHidden(el)))
                        )
                        .map((el) => (
                          <SelectItem key={el.id} value={el.id.toString()}>
                            {el.name}
                          </SelectItem>
                        ))}
                    </SelectContent>
                  </Select>
                </div>

                {unlock && (
                  <div className="items-baseline flex">
                    <h3 className="lg:text-xl font-semibold mr-2">{`Vượt thời gian: `}</h3>

                    <Input
                      type="number"
                      value={Number(minutes).toString()}
                      onChange={(ev) => setMinutes(parseInt(ev.target.value))}
                      className="text-lg  lg:text-xl  h-6 w-[120px]"
                    />

                    <h3 className="lg:text-xl font-semibold ml-2">{` phút`}</h3>
                  </div>
                )}

                <div className="items-baseline flex m-2">
                  <Checkbox
                    id="customer-request-2"
                    className="mr-1"
                    checked={customerRequest}
                    onCheckedChange={(value) =>
                      setCustomerRequest(value as boolean)
                    }
                  />

                  <label
                    htmlFor="customer-request-2"
                    className="lg:text-xl font-semibold"
                  >
                    Khách đòi
                  </label>
                </div>
              </div>

              <div>
                <h3 className="lg:text-xl font-semibold">
                  {`Số dịch vụ: ${selected.length}`}
                </h3>

                <h3 className="lg:text-xl font-semibold">
                  {`Tổng số tiền: ${formatPrice(tasksPrice(selected))}`}
                </h3>

                <h3 className="lg:text-xl font-semibold">
                  {`Tổng thời gian: ${tasksTime(selected) / 60} Phút`}
                </h3>

                {unlock && (
                  <h3 className="lg:text-xl font-semibold">
                    <b className="ml-2">
                      {work.onWorking
                        ? work.timeStart && (
                            <Timer
                              withText={true}
                              haveTime={work.time || 0}
                              timeStart={new Date(work.timeStart).getTime()}
                              labelAfterStart="Thời gian đã làm: "
                              labelBeforeStart="Thời gian chuẩn bị: "
                            />
                          )
                        : work.time && (
                            <>
                              <h3 className="lg:text-xl font-semibold">
                                {`Thời gian đã làm: ${formatTimeWithText(
                                  secondsToTime(work.time, false)
                                )}`}
                              </h3>
                              {work.timeStart && (
                                <h3 className="lg:text-xl font-semibold">
                                  {`Thời gian bắt đầu: ${formatTimeClock(
                                    new Date(work.timeStart)
                                  )}`}
                                </h3>
                              )}

                              {work.timeEnd && (
                                <h3 className="lg:text-xl font-semibold">
                                  {`Thời gian kết thúc: ${formatTimeClock(
                                    new Date(work.timeEnd)
                                  )}`}
                                </h3>
                              )}
                            </>
                          )}
                    </b>
                  </h3>
                )}
              </div>
            </div>

            <div className="mb-1 items-end flex flex-wrap min-h-[50px]">
              {selected.map((task, index) => (
                <Button
                  key={index}
                  variant="outline"
                  className="border-2 border-orange-500 mx-1"
                  onClick={() => {
                    setSelected((selected) =>
                      selected.filter((_, _index) => _index != index)
                    );
                  }}
                >
                  {task.name}
                  <Delete className="w-5 h-5 ml-2" />
                </Button>
              ))}
            </div>
          </div>

          <div className="">
            <Tabs value={tab} onValueChange={(value) => setTab(value)}>
              <TabsList className="flex-wrap items-center justify-start">
                {taskTypes.map((taskType: ITaskType) => (
                  <TabsTrigger key={taskType.id} value={taskType.id.toString()}>
                    {taskType.name}
                  </TabsTrigger>
                ))}
              </TabsList>
            </Tabs>
          </div>

          <div className="border grow overflow-auto">
            <div className="flex flex-wrap overflow-auto">
              {tasks
                .filter((el: ITask) => el.taskTypeId.toString() == tab)
                .map((el: ITask) => (
                  <div
                    key={el.id}
                    onClick={() => {
                      setSelected((selected) => [
                        ...new Set([...selected, el]),
                      ]);
                    }}
                    className="rounded-lg border border-2 border-orange-500  bg-card text-card-foreground shadow-sm max-h-[150px] h-[150px] w-[150px] m-2"
                  >
                    <div className="h-full w-full flex flex-col">
                      <div className="grow px-3 py-3 text-center flex flex-col justify-center">
                        <div className="lg::text-lg font-bold text-center overflow-clip">
                          {el.name}
                        </div>
                        <div>{`${el.time / 60} phút`}</div>
                      </div>
                      <div className="mt-2 w-full py-1 text-center bg-orange-600 text-white">
                        {formatPrice(el.price)}
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          </div>
        </div>

        <DialogFooter>
          <UnlockDialog unlock={unlock} setUnlock={setUnlock} />

          <Button
            disabled={loading}
            onClick={() => {
              setSelected(work.tasks || []);
              setCustomerRequest(work.customerRequest);
              setNewStaff(staff);
              setMinutes(0);
            }}
          >
            Mặc định
          </Button>

          <Button
            disabled={loading}
            onClick={() => {
              if (staff?.id) {
                setLoading(true);
                mutate({
                  id: work.id,
                  tasks: selected.map((el) => el.id),
                  customerRequest,
                  staffId: newStaff.id,
                  secOffset: minutes * 60,
                });
              }
            }}
          >
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Sửa công việc
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
