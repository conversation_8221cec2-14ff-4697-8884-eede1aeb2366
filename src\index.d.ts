type IInfo = {
  turn?: number;
  realTurn?: number;
  customersReq?: number;
  specTurn?: number;
  isLeave: boolean;
  onWorking: boolean;
  isBusy: boolean;
  startOn: Date;
  busyAt: Date;
  busyEndAt: Date;
};

type IStaff = {
  id: number;
  name: string;
  staffTypeId: number;
  works?: IWork[];
  today?: IInfo;
  info?: IInfo[];
};

type IStaffType = {
  id: number;
  name: string;
};

type ITaskType = {
  id: number;
  name: string;
  isDelete: boolean;
};

type ITask = {
  id: number;
  name: string;
  time: number;
  price: number;
  taskTypeId: number;
  isLove: boolean;
  taskType?: ITaskType;
};

type IWork = {
  id: number;
  customerRequest: boolean;
  onWorking: boolean;
  tasks?: ITask[];
  time?: number;
  timeStart?: string;
  timeEnd?: string;
  isSkip?: boolean;
  isSpecTurn?: boolean;
};

type ISettingData = {
  minPrice: number;
  convertCusReq: number;
  applyStart: number;
  applyEnd: number;
  applyRuleStaffType: number[];
  isDateApplyRule: boolean;
};

type IShop = {
  id: number;
  jwt: string;
  logoUrl: string;
  name: string;
  shopUrl: string;
};
