import {
  Table,
  TableBody,
  Table<PERSON>ell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';

import { useEffect, useState } from 'react';
import { useTaskType } from '@/hooks/useTaskType';
import { useTask } from '@/hooks/useTask';

// import { DeleteTask } from "./crud/delete-task";
// import { AddTask } from "./crud/add-task";
// import { EditTask } from "./crud/edit-task";
// import { AddTaskType } from "./crud/add-task-type";
// import { DeleteTaskType } from "./crud/delete-task-type";

import { formatPrice } from '@/lib/utils';
import { Loading } from '@/components/loading';
import { Button } from '@/components/ui/button';
import { Star } from 'lucide-react';
// import { useDrag, useDrop } from 'react-dnd';
import { useToast } from '@/components/ui/use-toast';
import { usePost } from '@/hooks/usePost';

export function Task() {
  const [tab, setTab] = useState<string>();
  const [tasks, setTasks] = useState<ITask[]>([]);

  const { data } = useTaskType();
  const { isPending, data: taskData, refetch: refetchTask } = useTask();

  const { toast } = useToast();

  useEffect(() => {
    if (data && data.length > 0) {
      if (!tab) {
        setTab(data[0].id.toString());
      } else {
        const _taskType = data.find((el) => el.id.toString() == tab);
        if (!_taskType) {
          setTab(data[0].id.toString());
        }
      }
    }
  }, [data]);

  useEffect(() => {
    if (taskData && taskData.length > 0) {
      setTasks(taskData);
    }
  }, [taskData]);

  const { mutate } = usePost({
    url: `/task/sort`,
    onError: () => {
      toast({
        variant: 'destructive',
        description: 'Có lỗi xảy ra. Vui lòng reload lại trang.',
      });
    },
  });

  const reorderRow = (draggedRowId: number, targetRowId: number) => {
    const draggedRowIndex = tasks.findIndex((el) => el.id == draggedRowId);
    const targetRowIndex = tasks.findIndex((el) => el.id == targetRowId);

    const _tasks = [...tasks];
    _tasks.splice(targetRowIndex, 0, _tasks.splice(draggedRowIndex, 1)[0]);

    mutate({ orders: _tasks.map((el) => el.id) });
    setTasks(_tasks);
  };

  if (isPending) return <Loading />;
  if (!taskData || !data) return <></>;

  return (
    <div>
      <Tabs value={tab} onValueChange={(value) => setTab(value)}>
        <div className="flex justify-between items-center">
          <TabsList>
            {data.map((taskType: ITaskType) => (
              <TabsTrigger key={taskType.id} value={taskType.id.toString()}>
                {taskType.name}
              </TabsTrigger>
            ))}

            {/* <AddTaskType refetch={refetch} /> */}
          </TabsList>

          {/* {tab && (
            <div className="flex justify-between items-center">
              <AddTask
                taskTypes={data}
                refetch={refetchTask}
                tab={parseInt(tab)}
              />
              <DeleteTaskType
                taskType={data.find((el) => el.id.toString() == tab)}
                refetch={refetch}
              />
            </div>
          )} */}
        </div>
      </Tabs>

      <Table>
        <TableHeader>
          <TableRow>
            {/* <TableHead className="text-center w-[50px]"></TableHead> */}
            <TableHead className="text-center w-[140px]">Yêu thích</TableHead>
            <TableHead className="w-[100px]">STT</TableHead>
            <TableHead className="w-[50%]">Tên dịch vụ</TableHead>
            <TableHead className="text-center">Thời gian</TableHead>
            <TableHead className="text-center">Giá thành</TableHead>
            <TableHead className="text-right">Hành động</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {tasks
            .filter((el: ITask) => el.taskTypeId.toString() == tab)
            .map((el: ITask, index: number) => (
              <DragableRow
                taskTypes={data}
                reorderRow={reorderRow}
                refetch={refetchTask}
                task={el}
                key={el.id}
                taskIndex={index}
              />
            ))}
        </TableBody>
      </Table>
    </div>
  );
}

function DragableRow({
  taskIndex,
  task,
}: {
  taskTypes: ITaskType[];
  taskIndex: number;
  task: ITask;
  refetch: () => void;
  reorderRow: (draggedRowId: number, targetRowId: number) => void;
}) {
  // const { toast } = useToast();

  // const { mutate } = usePost({
  //   url: '/task/love',
  //   onSuccess: () => {
  //     refetch();
  //   },
  //   onError: () => {
  //     toast({
  //       variant: 'destructive',
  //       description: 'Có lỗi xảy ra. Vui lòng reload lại trang.',
  //     });
  //   },
  // });

  // const [, dropRef] = useDrop({
  //   accept: 'row',
  //   drop: (draggedStaff: IStaff) => {
  //     reorderRow(draggedStaff.id, task.id);
  //   },
  // });

  // const [{ isDragging }, dragRef, previewRef] = useDrag({
  //   collect: (monitor) => ({
  //     isDragging: monitor.isDragging(),
  //   }),
  //   item: () => task,
  //   type: 'row',
  // });

  return (
    <TableRow
    // ref={previewRef}
    // className={` ${isDragging ? 'opacity-50' : 'opacity-100'}`}
    >
      {/* <TableCell ref={dropRef} className="text-center py-0 px-2">
        <Button size="icon" variant="ghost" ref={dragRef} className="cursor-move">
          <Grip className="w-4 h-4" />
        </Button>
      </TableCell> */}
      <TableCell
        className="text-center py-0 px-2"
        // ref={dropRef}
      >
        <Button
          size="icon"
          variant="ghost"
          // onClick={() => {
          //   mutate({ id: task.id });
          // }}
        >
          <Star
            color={task.isLove ? 'red' : 'black'}
            fill={task.isLove ? 'red' : 'white'}
            className="w-6 h-6"
          />
        </Button>
      </TableCell>
      <TableCell className="font-medium">{taskIndex + 1}</TableCell>
      <TableCell>{task.name}</TableCell>
      <TableCell className="text-center">{`${task.time / 60} phút`}</TableCell>
      <TableCell className="text-center">{formatPrice(task.price)}</TableCell>
      {/* <TableCell className="text-right">
        <EditTask taskTypes={taskTypes} task={task} refetch={refetch} />
        <DeleteTask task={task} refetch={refetch} />
      </TableCell> */}
    </TableRow>
  );
}
