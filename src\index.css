@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

#root {
  min-width: 100vw;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

::-webkit-scrollbar {
  display: none;
}

@layer base {
  :root {
    /* Modern gradient backgrounds */
    --background: 0 0% 100%;
    --background-gradient: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    --foreground: 222.2 84% 4.9%;

    /* Enhanced card styling */
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --card-gradient: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    /* Modern primary colors with gradients */
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --primary-gradient: linear-gradient(135deg, #1e293b 0%, #334155 100%);

    /* Refined secondary colors */
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --secondary-gradient: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    /* Enhanced accent colors */
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --accent-gradient: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    /* Softer borders and inputs */
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    /* Enhanced radius for modern look */
    --radius: 0.75rem;

    /* Glass morphism variables */
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);

    /* Animation variables */
    --transition-fast: 0.15s ease-out;
    --transition-normal: 0.3s ease-out;
    --transition-slow: 0.5s ease-out;
  }

  .dark {
    /* Dark mode gradient backgrounds */
    --background: 222.2 84% 4.9%;
    --background-gradient: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
    --foreground: 210 40% 98%;

    /* Dark mode card styling */
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --card-gradient: linear-gradient(135deg, #1e293b 0%, #334155 100%);

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    /* Dark mode primary colors */
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --primary-gradient: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);

    /* Dark mode secondary colors */
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --secondary-gradient: linear-gradient(135deg, #334155 0%, #475569 100%);

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    /* Dark mode accent colors */
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --accent-gradient: linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%);

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;

    /* Dark mode glass morphism */
    --glass-bg: rgba(0, 0, 0, 0.2);
    --glass-border: rgba(255, 255, 255, 0.1);
    --glass-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    background: var(--background-gradient);
    transition: all var(--transition-normal);
  }
}

/* Modern utility classes */
@layer utilities {
  .glass {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
  }

  .glass-strong {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
  }

  .glass-subtle {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .gradient-primary {
    background: var(--primary-gradient);
  }

  .gradient-secondary {
    background: var(--secondary-gradient);
  }

  .gradient-accent {
    background: var(--accent-gradient);
  }

  .gradient-card {
    background: var(--card-gradient);
  }

  .transition-fast {
    transition: all var(--transition-fast);
  }

  .transition-normal {
    transition: all var(--transition-normal);
  }

  .transition-slow {
    transition: all var(--transition-slow);
  }

  .hover-lift {
    transition: transform var(--transition-fast), box-shadow var(--transition-fast);
  }

  .hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  .dark .hover-lift:hover {
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
  }

  .glow {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }

  .glow-hover:hover {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.4);
  }

  .text-gradient {
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .border-gradient {
    border: 1px solid transparent;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(139, 92, 246, 0.2)) border-box;
    -webkit-mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: destination-out;
    mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
  }

  .shimmer {
    position: relative;
    overflow: hidden;
  }

  .shimmer::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: shimmer 2s infinite;
  }

  .dark .shimmer::before {
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  }
}
