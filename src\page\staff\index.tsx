import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';

import { Loading } from '@/components/loading';
// import { AddStaff } from './crud/add-staff';
// import { EditStaff } from './crud/edit-staff';
// import { DeleteStaff } from './crud/delete-staff';
// import { AddStaffType } from './crud/add-staff-type';
// import { DeleteStaffType } from './crud/delete-staff-type';

import { useStaff } from '@/hooks/useStaff';
import { useStaffType } from '@/hooks/useStaffType';

import { formatID } from '@/lib/utils';
import { useEffect, useState } from 'react';

export function Staff() {
  const [tab, setTab] = useState<string>();

  const { isPending, data } = useStaff();
  const { data: staffTypes } = useStaffType();

  useEffect(() => {
    if (staffTypes && staffTypes.length > 0) {
      if (!tab) {
        setTab(staffTypes[0].id.toString());
      } else {
        const _staffType = staffTypes.find((el) => el.id.toString() == tab);
        if (!_staffType) {
          setTab(staffTypes[0].id.toString());
        }
      }
    }
  }, [staffTypes]);

  if (isPending) return <Loading />;
  if (!data || !staffTypes) return <></>;

  return (
    <div>
      <Tabs value={tab} onValueChange={(value) => setTab(value)}>
        <div className="flex justify-between items-center">
          <TabsList>
            {staffTypes.map((el) => (
              <TabsTrigger key={el.id} value={el.id.toString()}>
                {el.name}
              </TabsTrigger>
            ))}

            {/* <AddStaffType refetch={typeRefetch} /> */}
          </TabsList>

          {/* {tab && (
            <div className="flex justify-between items-center">
              <AddStaff staffTypes={staffTypes} refetch={refetch} tab={tab} />
              <DeleteStaffType
                staffType={staffTypes.find((el) => el.id.toString() == tab)}
                refetch={typeRefetch}
              />
            </div>
          )} */}
        </div>
      </Tabs>

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[100px]">STT</TableHead>
            <TableHead className="w-[100px]">ID</TableHead>
            <TableHead className="w-[50%]">Họ và tên</TableHead>
            <TableHead>Công việc</TableHead>
            {/* <TableHead className="text-right">Hành động</TableHead> */}
          </TableRow>
        </TableHeader>
        <TableBody>
          {data
            .filter((el) => el.staffTypeId.toString() == tab)
            .map((el: IStaff, index: number) => (
              <TableRow key={el.id}>
                <TableCell className="font-medium">{index + 1}</TableCell>
                <TableCell>{formatID(el.id)}</TableCell>
                <TableCell>{el.name}</TableCell>
                <TableCell>
                  {staffTypes.find((staffType) => staffType.id == el.staffTypeId)?.name || ''}
                </TableCell>
                {/* <TableCell className="text-right">
                  <EditStaff staff={el} refetch={refetch} />
                  <DeleteStaff staff={el} refetch={refetch} />
                </TableCell> */}
              </TableRow>
            ))}
        </TableBody>
      </Table>
    </div>
  );
}
