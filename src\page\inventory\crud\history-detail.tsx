import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableCell,
  TableBody,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { differenceInDays, format } from "date-fns";
import { useEffect, useState } from "react";
import { TransactionType } from "./create-transaction";
import { useGet } from "@/hooks/useGet";
import { Pagination } from "@/components/pagination";
import TransactionDetail from "./transactions-detail";

type ITransactionDetail = {
  id: number;
  productId: number;
  quantity: number;
  note: string;
  transactionReceipt: {
    id: number;
    transactionDate: string;
    note: string;
    transactionType: TransactionType;
    staff?: {
      id: number;
      name: string;
    };
    staffId?: number;
  };
};

type HistoryDetailProps = {
  enable: boolean;
  product: IProduct;
  fromDate: Date | undefined;
  toDate: Date | undefined;
};

export function HistoryDetail({
  enable,
  product,
  fromDate,
  toDate,
}: HistoryDetailProps) {
  const [staffName, setStaffName] = useState("");
  const [debouncedStaffName, setDebouncedStaffName] = useState("");

  const [transactionType, setTransactionType] = useState(TransactionType.NONE);
  const [data, setData] = useState<ITransactionDetail[]>([]);

  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const { data: rawData } = useGet({
    enabled: enable,
    url: "/inventory/transaction",
    params: {
      productId: product.id,
      fromDateOffset: fromDate ? differenceInDays(new Date(), fromDate) : 0,
      toDateOffset: toDate ? differenceInDays(new Date(), toDate) : undefined,
      transactionType:
        transactionType == TransactionType.NONE ? undefined : transactionType,
      staffName:
        debouncedStaffName.trim() === "" ? undefined : debouncedStaffName,
      page: currentPage,
    },
  });

  useEffect(() => {
    if (rawData) {
      setData(rawData.data);
      setTotalPages(rawData.totalPages);
    }
  }, [rawData]);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedStaffName(staffName);
    }, 300);

    return () => {
      clearTimeout(handler);
    };
  }, [staffName]);

  return (
    <div className="">
      <div className="">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[100px]">STT</TableHead>
              <TableHead className="w-[240px]">Thời gian</TableHead>
              <TableHead className="w-[240px]">
                <div className="flex flex-row gap-4 items-center">
                  <span>Loại</span>
                  <Select
                    value={transactionType}
                    onValueChange={(value) =>
                      setTransactionType(value as TransactionType)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={TransactionType.NONE}>
                        Tất cả
                      </SelectItem>
                      <SelectItem value={TransactionType.IMPORT}>
                        Nhập
                      </SelectItem>
                      <SelectItem value={TransactionType.EXPORT}>
                        Xuất
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </TableHead>
              <TableHead className="">
                <div className="flex flex-row items-center gap-4">
                  <span>Tên nhân viên</span>
                  <Input
                    value={staffName}
                    onChange={(event) => setStaffName(event.target.value)}
                    placeholder="Tìm tên"
                    className="max-w-[200px]"
                  />
                </div>
              </TableHead>
              <TableHead className="w-[150px]">Số lượng</TableHead>
              <TableHead className="w-[150px]">Đơn vị</TableHead>
              <TableHead className="w-[100px]">Chi tiết</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.map((el, index) => (
              <TableRow key={el.id}>
                <TableCell className="font-medium">{index + 1}</TableCell>
                <TableCell>
                  {format(
                    new Date(el.transactionReceipt.transactionDate),
                    "HH:mm:ss, dd/MM/yyyy"
                  )}
                </TableCell>
                <TableCell>
                  {el.transactionReceipt.transactionType ==
                  TransactionType.IMPORT
                    ? "Nhập hàng"
                    : "Xuất hàng"}
                </TableCell>
                <TableCell>
                  {el.transactionReceipt.staff?.name
                    ? el.transactionReceipt.staff?.name
                    : "Manager"}
                </TableCell>
                <TableCell>{el.quantity}</TableCell>
                <TableCell>{product.unit}</TableCell>
                <TableCell>
                  <TransactionDetail id={el.transactionReceipt.id} />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={(page) => setCurrentPage(page)}
      />
    </div>
  );
}
