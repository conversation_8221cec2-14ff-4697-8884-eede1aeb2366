import { colorWork, isApplyRule } from "@/lib/utils";
import { DeleteWork } from "./crud/delete-work";
import { WorkDetail } from "./crud/work-detail";
import { useData } from "./data-provider";

type ComponentProps = {
  staff: IStaff;
  refetch: () => void;
};

function filterWorks(staff: IStaff, works: IWork[], settingData: ISettingData) {
  const _isApplyRule = isApplyRule(staff.staffTypeId, settingData);

  let _counter = 0;

  return works.filter((work) => {
    if (_isApplyRule) {
      if (!work.onWorking && work.customerRequest) {
        _counter += 1;

        if (_counter % settingData.convertCusReq == 0) {
          return true;
        } else {
          return false;
        }
      }

      if (work.isSpecTurn) return false;
      return true;
    }

    if (!work.onWorking && work.customerRequest) return false;
    if (work.isSpecTurn) return false;

    return true;
  });
}

export function WorkData({ staff, refetch }: ComponentProps) {
  const works: IWork[] = staff.works || [];
  const { isEditable, settingData } = useData();

  return (
    <div className="flex">
      {filterWorks(staff, works, settingData).map((work) => (
        <div
          key={work.id}
          className={`w-[80px] min-h-5 m-2 border ${colorWork(work)}`}
        >
          <div className="h-full flex flex-col justify-between px-2 py-1">
            <div className="flex items-center justify-between">
              <div>
                {isEditable && (
                  <DeleteWork refetch={refetch} staff={staff} work={work} />
                )}
              </div>

              <div>
                {!work.onWorking && work.customerRequest ? (
                  <div className="text-xs">{`${settingData.convertCusReq} KĐ`}</div>
                ) : (
                  !work.isSkip && (
                    <WorkDetail refetch={refetch} staff={staff} work={work} />
                  )
                )}
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
