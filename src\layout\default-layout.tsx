import { ReactNode } from 'react';
import { NavBar } from '@/components/menu-bar';
// import { Sidebar } from "@/components/side-bar";
import './bg.css';

type LayoutProps = {
  children: ReactNode;
};

export function DefaultLayout({ children }: LayoutProps) {
  return (
    <>
      <div className="fixed top-0 bottom-0 left-0 right-0 watermark z-10"></div>

      <NavBar />

      <div className="overflow-auto grow flex items-sketch mb-20">
        {/* <div className="hidden lg:block">
          <Sidebar />
        </div> */}

        <main className="grow px-5 flex flex-col">{children}</main>
      </div>

      <div className="w-full fixed right-0 bottom-0">
        {/* <div className="text-base md:text-xl text-pretty text-end p-2.5 pr-5">
          Copyright © 2024 Cườ<PERSON> & <PERSON><PERSON><PERSON>
        </div> */}
      </div>
    </>
  );
}
