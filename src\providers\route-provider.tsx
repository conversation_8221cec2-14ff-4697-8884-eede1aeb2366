import NotFound from '@/components/error/404';
import { Page } from '@/page';
import { General } from '@/page/general';
import { SignIn } from '@/page/sign-in';
import { Staff } from '@/page/staff';
import { Chart } from '@/page/stats/chart';
import { Task } from '@/page/task';

import { Routes, Route } from 'react-router';
import { BrowserRouter } from 'react-router-dom';
import { StatsRangeDay } from '@/page/stats/range-day';
import { Main } from '@/page/main';
import { ChangePassword } from '@/page/change-pass';
import { Setting } from '@/page/setting';
import { Inventory } from '@/page/inventory';
import LowStockList from '@/page/warning';

export function RouteProvider() {
  return (
    <BrowserRouter basename="/">
      <Routes>
        <Route path="*" element={<NotFound />} />

        <Route path="/" element={<Page />}>
          <Route path="/" element={<Main />}>
            <Route path="/turn" element={<General />} />
            <Route path="/staff" element={<Staff />} />
            <Route path="/task" element={<Task />} />
            <Route path="/stats/chart" element={<Chart />} />
            <Route path="/stats/detail" element={<StatsRangeDay />} />
            <Route path="/change-password" element={<ChangePassword />} />
            <Route path="/setting" element={<Setting />} />
            <Route path="/inventory" element={<Inventory />} />
            <Route path="/lowstock" element={<LowStockList />} />
          </Route>

          <Route path="/sign-in" element={<SignIn />} />
        </Route>
      </Routes>
    </BrowserRouter>
  );
}
