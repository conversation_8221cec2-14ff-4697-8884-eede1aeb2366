import {
  Dialog,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";

import { usePost } from "@/hooks/usePost";
import { Loader2 } from "lucide-react";
import { useState } from "react";
import { useToast } from "@/components/ui/use-toast";

type ComponentProps = {
  productType: IProductType;
  refetch: () => void;
};

export function DeleteProductType({ productType, refetch }: ComponentProps) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  const { toast } = useToast();

  const { mutate } = usePost({
    url: "inventory/product/type/delete",
    onSuccess: () => {
      setOpen(false);
      setLoading(false);
      refetch();
    },
    onError: () => {
      toast({
        variant: "destructive",
        description: "Có lỗi xảy ra. Vui lòng reload lại trang.",
      });
      setLoading(false);
    },
  });

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="destructive" className="my-4 mx-1">
          Xoá loại sản phẩm
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Xoá loại sản phẩm</DialogTitle>
        </DialogHeader>
        <DialogDescription></DialogDescription>

        <div>
          Bạn có chắc muốn xoá loại sản phẩm <b>{productType?.name}</b> không?
        </div>

        <DialogFooter>
          <Button
            disabled={loading}
            variant="destructive"
            onClick={() => {
              if (productType?.id) {
                setLoading(true);
                mutate({ id: productType.id });
              }
            }}
          >
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Xoá loại
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
