import {
  BarChart3,
  Bath,
  CircleAlert,
  House,
  List,
  LockKeyhole,
  Notebook,
  Settings,
} from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Link } from 'react-router-dom';

const MENU_TYPE = {
  GENERAL: 0,
  MANAGER: 1,
  STATS: 2,
  ACCOUNT: 3,
};

const sections = [
  { title: 'Chung', type: MENU_TYPE.GENERAL },
  { title: 'Quản lý', type: MENU_TYPE.MANAGER },
  { title: 'Thống kê', type: MENU_TYPE.STATS },
  { title: 'Tài khoản', type: MENU_TYPE.ACCOUNT },
];

export const menu = [
  {
    path: '/lowstock',
    label: 'Cảnh báo',
    icon: <CircleAlert className="mr-2 h-4 w-4" />,
    type: MENU_TYPE.GENERAL,
  },
  {
    path: '/turn',
    label: '<PERSON><PERSON><PERSON> turn',
    icon: <House className="mr-2 h-4 w-4" />,
    type: MENU_TYPE.MANAGER,
  },
  {
    path: '/inventory',
    label: 'Nhà kho',
    icon: <List className="mr-2 h-4 w-4" />,
    type: MENU_TYPE.MANAGER,
  },
  {
    path: '/staff',
    label: 'Nhân viên',
    icon: <List className="mr-2 h-4 w-4" />,
    type: MENU_TYPE.MANAGER,
  },
  {
    path: '/task',
    label: 'Dịch vụ',
    icon: <Bath className="mr-2 h-4 w-4" />,
    type: MENU_TYPE.MANAGER,
  },
  {
    path: '/setting',
    label: 'Cài đặt',
    icon: <Settings className="mr-2 h-4 w-4" />,
    type: MENU_TYPE.MANAGER,
  },
  {
    path: '/stats/chart',
    label: 'Biểu đồ',
    icon: <BarChart3 className="mr-2 h-4 w-4" />,
    type: MENU_TYPE.STATS,
  },
  // {
  //   path: "/stats/table",
  //   label: "Bảng",
  //   icon: <Notebook className="mr-2 h-4 w-4" />,
  //   type: MENU_TYPE.STATS,
  // },
  {
    path: '/stats/detail',
    label: 'Chi tiết',
    icon: <Notebook className="mr-2 h-4 w-4" />,
    type: MENU_TYPE.STATS,
  },
  {
    path: '/change-password',
    label: 'Đổi mật khẩu',
    icon: <LockKeyhole className="mr-2 h-4 w-4" />,
    type: MENU_TYPE.ACCOUNT,
  },
];

type CompoenntProps = {
  setOpen?: (arg: boolean) => void;
};

export function Sidebar({ setOpen }: CompoenntProps) {
  return (
    <div className="mt-6">
      <div className="space-y-6 py-4">
        {sections.map((section) => (
          <div key={section.type} className="px-2">
            <h2 className="mb-3 px-3 text-sm font-semibold tracking-wide uppercase text-muted-foreground">
              {section.title}
            </h2>
            <div className="space-y-1">
              {menu
                .filter((el) => el.type === section.type)
                .map((el, index) => (
                  <Link key={index} to={el.path} onClick={() => setOpen?.(false)}>
                    <Button
                      variant={location.pathname === el.path ? 'secondary' : 'ghost'}
                      size="sm"
                      className={`w-full justify-start transition-normal hover-lift ${
                        location.pathname === el.path
                          ? 'gradient-secondary shadow-sm'
                          : 'hover:bg-accent/50'
                      }`}
                    >
                      <span className="mr-3 flex-shrink-0">{el.icon}</span>
                      <span className="truncate">{el.label}</span>
                    </Button>
                  </Link>
                ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
