import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useToast } from "@/components/ui/use-toast";

import { usePost } from "@/hooks/usePost";

import { Loader2 } from "lucide-react";
import { useState } from "react";

type ComponentProps = {
  refetch: () => void;
  staff: IStaff;
  work: IWork;
};

export function ContinueWork({ refetch, staff, work }: ComponentProps) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  const { toast } = useToast();

  const { mutate } = usePost({
    url: "/work/continue",
    onSuccess: () => {
      setLoading(false);
      refetch();
      setOpen(false);
    },
    onError: () => {
      toast({
        variant: "destructive",
        description: "<PERSON>ó lỗi xảy ra. Vui lòng reload lại trang.",
      });
      setLoading(false);
    },
  });

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>Tiếp tục công việc</Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Tiếp tục công việc</DialogTitle>
        </DialogHeader>
        <DialogDescription></DialogDescription>

        <div className="">
          Bạn có chắc chắn muốn <b>{` TIẾP TỤC `}</b> công việc của
          <b>{` ${staff.name} `}</b>không?
        </div>
        <DialogFooter>
          <Button
            variant="secondary"
            disabled={loading}
            onClick={() => {
              setOpen(false);
            }}
          >
            Không
          </Button>

          <Button
            disabled={loading}
            variant="destructive"
            onClick={() => {
              if (staff.today?.onWorking) {
                toast({
                  variant: "destructive",
                  description:
                    "Nhân viên đang trong công việc khác. Vui lòng kết thúc công việc khác truowscF",
                });
              } else if (staff?.id) {
                setLoading(true);
                mutate({ id: work.id });
              }
            }}
          >
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Có, tiếp tục công việc
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
