import { Button } from '@/components/ui/button';

type PaginationProps = {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  siblingCount?: number; // Số trang hiển thị gần currentPage
};

export function Pagination({
  currentPage,
  totalPages,
  onPageChange,
  siblingCount = 1,
}: PaginationProps) {
  const DOTS = '...';

  const generatePages = () => {
    const totalNumbers = siblingCount * 2 + 5;
    // const totalBlocks = totalNumbers - 2;

    if (totalPages <= totalNumbers) {
      return Array.from({ length: totalPages }, (_, i) => i + 1);
    }

    const pages: (number | string)[] = [];

    const leftSiblingIndex = Math.max(currentPage - siblingCount, 2);
    const rightSiblingIndex = Math.min(currentPage + siblingCount, totalPages - 1);

    pages.push(1); // Always show first page

    if (leftSiblingIndex > 2) {
      pages.push(DOTS);
    }

    for (let i = leftSiblingIndex; i <= rightSiblingIndex; i++) {
      pages.push(i);
    }

    if (rightSiblingIndex < totalPages - 1) {
      pages.push(DOTS);
    }

    pages.push(totalPages); // Always show last page

    return pages;
  };

  const pages = generatePages();

  return (
    <div className="flex items-center justify-center gap-2 mt-4">
      <Button
        variant="outline"
        size="sm"
        onClick={() => {
          if (currentPage <= 1) return;
          onPageChange(currentPage - 1);
        }}
        disabled={currentPage <= 1}
      >
        &larr; Trước
      </Button>

      {pages.map((page, index) =>
        page === DOTS ? (
          <span key={index} className="px-2 text-sm text-muted-foreground">
            ...
          </span>
        ) : (
          <Button
            key={index}
            variant={page === currentPage ? 'default' : 'outline'}
            size="sm"
            onClick={() => onPageChange(Number(page))}
          >
            {page}
          </Button>
        ),
      )}

      <Button
        variant="outline"
        size="sm"
        onClick={() => {
          if (currentPage >= totalPages) return;
          onPageChange(currentPage + 1);
        }}
        disabled={currentPage >= totalPages}
      >
        Sau &rarr;
      </Button>
    </div>
  );
}
