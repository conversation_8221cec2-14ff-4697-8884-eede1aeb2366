import { Loader2 } from "lucide-react";

export function Loading() {
  return (
    <div className="h-full w-full grow flex items-center justify-center">
      <div className="flex flex-col items-center space-y-4">
        <div className="relative">
          <div className="w-16 h-16 border-4 border-primary/20 border-t-primary rounded-full animate-spin"></div>
          <div className="absolute inset-0 w-16 h-16 border-4 border-transparent border-r-accent rounded-full animate-spin animation-delay-150"></div>
        </div>
        <div className="text-sm text-muted-foreground animate-pulse">
          <PERSON><PERSON> tải...
        </div>
      </div>
    </div>
  );
}
