import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>T<PERSON>le,
  DialogTrigger,
} from "@/components/ui/dialog";

import { usePost } from "@/hooks/usePost";
import { useEffect, useState } from "react";

import { Delete, Loader2 } from "lucide-react";
import { formatID, formatPrice, tasksPrice, tasksTime } from "@/lib/utils";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/components/ui/use-toast";
import { Input } from "@/components/ui/input";
import { useData } from "../data-provider";

type ComponentProps = {
  refetch: () => void;
  staff: IStaff;
};

export function StartWork({ refetch, staff }: ComponentProps) {
  const [tab, setTab] = useState<string>();
  const [open, setOpen] = useState(false);

  const { toast } = useToast();

  const [selected, setSelected] = useState<ITask[]>([]);
  const [customerRequest, setCustomerRequest] = useState(false);
  const [loading, setLoading] = useState(false);
  const [minutes, setMinutes] = useState(0);

  const { mutate } = usePost({
    url: "/work/start",
    onSuccess: () => {
      setLoading(false);
      refetch();
      setOpen(false);
    },
    onError: () => {
      toast({
        variant: "destructive",
        description: "Có lỗi xảy ra. Vui lòng reload lại trang.",
      });
      setLoading(false);
    },
  });

  const { tasks, taskTypes } = useData();

  useEffect(() => {
    setSelected([]);
  }, [open]);

  useEffect(() => {
    if (taskTypes?.length > 0 && !tab) {
      setTab(taskTypes[0].id.toString());
    }
  }, [taskTypes]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button size="sm" variant="default" className="m-1">
          Bắt đầu
        </Button>
      </DialogTrigger>

      <DialogContent className="flex flex-col justify-between min-w-[90%] max-w-[90%] min-h-[90%] max-h-[90%] overflow-auto">
        <DialogHeader>
          <DialogTitle>Bắt đầu công việc</DialogTitle>
        </DialogHeader>

        <DialogDescription></DialogDescription>

        <div className="grow h-full flex flex-col overflow-auto">
          <div>
            <div className="grid grid-cols-2 mb-2">
              <div>
                <h3 className="lg:text-xl font-semibold">
                  {`ID Nhân viên: ${formatID(staff.id)}`}
                </h3>

                <h3 className="lg:text-xl font-semibold">
                  {`Họ và tên: ${staff.name}`}
                </h3>

                <div className="items-baseline flex">
                  <h3 className="lg:text-xl font-semibold mr-2">{`Bắt đầu sau: `}</h3>

                  <Input
                    type="number"
                    value={Number(minutes).toString()}
                    onChange={(ev) => setMinutes(parseInt(ev.target.value))}
                    className="text-lg  lg:text-xl  h-6 w-[120px]"
                  />

                  <h3 className="lg:text-xl font-semibold ml-2">{` phút`}</h3>
                </div>

                <div className="items-center flex m-2">
                  <Checkbox
                    id="customer-request-2"
                    className="mr-1"
                    checked={customerRequest}
                    onCheckedChange={(value) =>
                      setCustomerRequest(value as boolean)
                    }
                  />

                  <label
                    htmlFor="customer-request-2"
                    className="lg:text-xl font-semibold"
                  >
                    Khách đòi
                  </label>
                </div>
              </div>

              <div>
                <h3 className="lg:text-xl font-semibold">
                  {`Số dịch vụ: ${selected.length}`}
                </h3>

                <h3 className="lg:text-xl font-semibold">
                  {`Tổng số tiền: ${formatPrice(tasksPrice(selected))}`}
                </h3>

                <h3 className="lg:text-xl font-semibold">
                  {`Tổng thời gian: ${tasksTime(selected) / 60} Phút`}
                </h3>
              </div>
            </div>

            <div className="mb-1 items-end flex flex-wrap min-h-[50px]">
              {selected.map((task, index) => (
                <Button
                  key={index}
                  variant="outline"
                  className="border-2 border-orange-500 mx-1"
                  onClick={() => {
                    setSelected((selected) =>
                      selected.filter((_, _index) => _index != index)
                    );
                  }}
                >
                  {task.name}
                  <Delete className="w-5 h-5 ml-2" />
                </Button>
              ))}
            </div>
          </div>

          <div className="">
            <Tabs value={tab} onValueChange={(value) => setTab(value)}>
              <TabsList className="flex-wrap items-center justify-start">
                {taskTypes.map((taskType: ITaskType) => (
                  <TabsTrigger key={taskType.id} value={taskType.id.toString()}>
                    {taskType.name}
                  </TabsTrigger>
                ))}
              </TabsList>
            </Tabs>
          </div>

          <div className="border grow overflow-auto">
            <div className="flex flex-wrap overflow-auto">
              {tasks
                .filter((el: ITask) => el.taskTypeId.toString() == tab)
                .map((el: ITask) => (
                  <div
                    key={el.id}
                    onClick={() => {
                      setSelected((selected) => [
                        ...new Set([...selected, el]),
                      ]);
                    }}
                    className="rounded-lg border border-2 border-orange-500  bg-card text-card-foreground shadow-sm max-h-[150px] h-[150px] w-[150px] m-2"
                  >
                    <div className="h-full w-full flex flex-col">
                      <div className="grow px-3 py-3 text-center flex flex-col justify-center">
                        <div className="lg::text-lg font-bold text-center overflow-clip">
                          {el.name}
                        </div>
                        <div>{`${el.time / 60} phút`}</div>
                      </div>
                      <div className="mt-2 w-full py-1 text-center bg-orange-600 text-white">
                        {formatPrice(el.price)}
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button
            disabled={loading}
            onClick={() => {
              if (staff?.id) {
                setLoading(true);
                mutate({
                  staffId: staff?.id,
                  tasks: selected.map((el) => el.id),
                  customerRequest,
                  startAfterSec: minutes * 60,
                });
              }
            }}
          >
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Bắt đầu công việc
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
