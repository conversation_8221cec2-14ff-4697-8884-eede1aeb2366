import { useEffect, useMemo, useState } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { useShop } from '@/hooks/useShop';
import axios from 'axios';

type IShopData = {
  shop: IShop;
  products: IProduct[];
};

export default function LowStockList() {
  const { shops } = useShop();

  const [selectedShop, setSelectedShop] = useState<IShopData | null>(null);
  const [shopData, setShopData] = useState<IShopData[]>([]);

  useEffect(() => {
    async function fetchData() {
      if (shops?.length) {
        const data = [];

        for (const shop of shops) {
          const response = await axios.get(`${shop.shopUrl}/api/inventory/product`, {
            headers: {
              Authorization: `Bearer ${shop.jwt}`,
            },
          });

          if (response.data.length >= 0) {
            const products = response.data.filter((p: IProduct) => p.quantity <= p.warningQuantity);
            if (products.length > 0) data.push({ shop, products });
          }
        }

        setShopData(data);
      }
    }

    fetchData();
  }, [shops]);

  const selectedShopData = useMemo(() => {
    if (selectedShop) return [selectedShop];
    return shopData;
  }, [selectedShop, shopData]);

  const haveLowStock = useMemo(() => {
    return selectedShopData.some((el) => el.products.length > 0);
  }, [selectedShopData]);

  return (
    <div className="p-6 space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="text-lg font-semibold">Sản phẩm tồn kho thấp</CardTitle>
          <div className="flex flex-wrap gap-2 mt-3">
            <Button
              variant={selectedShop === null ? 'default' : 'outline'}
              onClick={() => setSelectedShop(null)}
            >
              Tất cả
            </Button>
            {shopData.map((el) => (
              <Button
                key={el.shop.id}
                variant={selectedShop?.shop.id === el.shop.id ? 'default' : 'outline'}
                onClick={() => setSelectedShop(el)}
              >
                {el.shop.name}
              </Button>
            ))}
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {haveLowStock ? (
            selectedShopData.map(
              (el) =>
                el.products.length > 0 && (
                  <div key={el.shop.id} className="space-y-2">
                    <h2 className="font-semibold text-base">{el.shop.name}</h2>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="w-[50%]">Sản phẩm</TableHead>
                          <TableHead className="w-[20%] text-right">Số lượng</TableHead>
                          <TableHead className="text-right">Ngưỡng cảnh báo</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {el.products.map((p) => (
                          <TableRow key={p.id} className="hover:bg-muted/50">
                            <TableCell>{p.name}</TableCell>
                            <TableCell className="text-right font-medium">{p.quantity}</TableCell>
                            <TableCell className="text-right">{p.warningQuantity}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                ),
            )
          ) : (
            <p className="text-center text-muted-foreground">Không có sản phẩm nào dưới ngưỡng</p>
          )}
          {/* // <p className="text-center text-muted-foreground">Không có sản phẩm nào dưới ngưỡng</p> */}
        </CardContent>
      </Card>
    </div>
  );
}
