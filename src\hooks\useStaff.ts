import { differenceInDays } from "date-fns";
import { useGet } from "./useGet";

export function useStaff() {
  const { data, ...props } = useGet({ url: "/staff" });
  return { ...props, data: data as IStaff[] };
}

export function useStaffDate(date: Date) {
  const dateOffset = differenceInDays(new Date(), date);
  const { data, ...props } = useGet({
    url: "/staff/info",
    params: { dateOffset },
  });
  return { ...props, data: data as IStaff[] };
}

type StatsParams = {
  fromDateOffset: number;
  toDateOffset?: number;
};

export function useStaffStats(fromDate: Date, toDate?: Date) {
  const params: StatsParams = {
    fromDateOffset: differenceInDays(new Date(), fromDate),
  };

  if (toDate) {
    params.toDateOffset = differenceInDays(new Date(), toDate);
  }

  const { data, ...props } = useGet({ url: "/staff/stats", params: params });
  return { ...props, data: data as IStaff[] };
}

export function useOneStaff(id: number, enabled = false) {
  const { data, ...props } = useGet({ url: `/staff/info/${id}`, enabled });
  return { ...props, data: data as IStaff };
}
