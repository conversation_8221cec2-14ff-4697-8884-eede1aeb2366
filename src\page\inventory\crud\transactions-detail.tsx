import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useGet } from "@/hooks/useGet";
import { format } from "date-fns";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useEffect, useState } from "react";
import { TransactionType } from "./create-transaction";

type TransactionReceipt = {
  id: number;
  transactionDate: string;
  note: string;
  transactionType: TransactionType;
  staff?: {
    id: number;
    name: string;
  };
  staffId?: number;
  details: {
    id: number;
    productId: number;
    quantity: number;
    note: string;
    product: {
      id: number;
      name: string;
      unit: string;
    };
  }[];
};

export default function TransactionDetail({ id }: { id: number }) {
  const [open, setOpen] = useState(false);
  const [data, setData] = useState<TransactionReceipt>();

  const { data: rawData } = useGet({
    enabled: open,
    url: `/inventory/transaction/detail/${id}`,
  });

  useEffect(() => {
    if (rawData) {
      setData(rawData);
    }
  }, [rawData]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="default" className="m-1">
          Chi tiết
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-3xl">
        <DialogHeader>
          <DialogTitle>Chi tiết phiếu</DialogTitle>
        </DialogHeader>
        <DialogDescription></DialogDescription>

        {data && (
          <div className="max-w-5xl mx-auto p-6 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-2xl font-bold">
                  {data.transactionType === "IMPORT"
                    ? "Chi tiết phiếu nhập"
                    : "Chi tiết phiếu xuất"}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4 text-lg">
                  <div>
                    <strong>Mã phiếu:</strong> {id}
                  </div>
                  <div>
                    <strong>Ngày tạo:</strong>{" "}
                    {format(
                      new Date(data.transactionDate),
                      "HH:mm:ss, dd/MM/yyyy"
                    )}
                  </div>
                  <div>
                    <strong>Nhân viên:</strong> {data.staff?.name || "Manager"}
                  </div>
                  <div>
                    <strong>Ghi chú:</strong> {data.note || "—"}
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-xl font-semibold">
                  Danh sách sản phẩm
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>STT</TableHead>
                      <TableHead>Tên sản phẩm</TableHead>
                      <TableHead>Số lượng</TableHead>
                      <TableHead>Đơn vị</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {data.details?.map((item, index: number) => (
                      <TableRow key={index}>
                        <TableCell>{index + 1}</TableCell>
                        <TableCell>{item.product?.name}</TableCell>
                        <TableCell>{item.quantity}</TableCell>
                        <TableCell>{item.product?.unit}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
